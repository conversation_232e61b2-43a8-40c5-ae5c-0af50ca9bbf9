{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from \"@supabase/supabase-js\";\n\n// Supabase configuration with fallback values for debugging\nconst supabaseUrl =\n  process.env.NEXT_PUBLIC_SUPABASE_URL;\nconst supabaseAnonKey =\n  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n\n// Debug environment variables (remove after testing)\nconsole.log(\"🔍 Environment Debug:\");\nconsole.log(\"- NODE_ENV:\", process.env.NODE_ENV);\nconsole.log(\n  \"- URL from env:\",\n  process.env.NEXT_PUBLIC_SUPABASE_URL ? \"✅ Loaded\" : \"❌ Missing\"\n);\nconsole.log(\n  \"- Anon Key from env:\",\n  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? \"✅ Loaded\" : \"❌ Missing\"\n);\nconsole.log(\"- Final URL:\", supabaseUrl);\nconsole.log(\n  \"- Final Anon Key (first 20 chars):\",\n  supabaseAnonKey?.substring(0, 20) + \"...\"\n);\n\n// Validate final values\nif (!supabaseUrl) {\n  throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\n\nif (!supabaseAnonKey) {\n  throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\n\n// Create Supabase client for client-side operations\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n  },\n  realtime: {\n    params: {\n      eventsPerSecond: 10,\n    },\n  },\n});\n\n// Create Supabase client with service role for server-side operations\n// Only use this for server-side operations that require elevated permissions\nexport const supabaseAdmin =\n  typeof window === \"undefined\" && supabaseServiceRoleKey\n    ? createClient(supabaseUrl, supabaseServiceRoleKey, {\n        auth: {\n          autoRefreshToken: false,\n          persistSession: false,\n        },\n      })\n    : null;\n\n// Database and Storage references for convenience\nexport const db = supabase;\nexport const storage = supabase.storage;\n\n// Log configuration (without sensitive data)\nexport const logSupabaseConfig = () => {\n  console.log(\"Supabase Configuration:\");\n  console.log(\"- URL:\", supabaseUrl);\n  console.log(\"- Environment:\", process.env.NODE_ENV);\n  console.log(\"- Client initialized:\", !!supabase);\n};\n\n// Initialize Supabase debugging\nexport const initSupabase = () => {\n  console.log(\"🚀 Supabase initialized\");\n  logSupabaseConfig();\n\n  // Test connection on initialization\n  if (typeof window !== \"undefined\") {\n    console.log(\"Client-side Supabase ready\");\n  } else {\n    console.log(\"Server-side Supabase ready\");\n  }\n};\n\nexport default supabase;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA,4DAA4D;AAC5D,MAAM;AAEN,MAAM;AAEN,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB;AAEpE,qDAAqD;AACrD,QAAQ,GAAG,CAAC;AACZ,QAAQ,GAAG,CAAC;AACZ,QAAQ,GAAG,CACT,mBACA,uCAAuC;AAEzC,QAAQ,GAAG,CACT,wBACA,uCAA4C;AAE9C,QAAQ,GAAG,CAAC,gBAAgB;AAC5B,QAAQ,GAAG,CACT,sCACA,iBAAiB,UAAU,GAAG,MAAM;AAGtC,wBAAwB;AACxB,uCAAkB;;AAElB;AAEA,uCAAsB;;AAEtB;AAGO,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACjE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,UAAU;QACR,QAAQ;YACN,iBAAiB;QACnB;IACF;AACF;AAIO,MAAM,gBACX,gBAAkB,eAAe,yBAC7B,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,wBAAwB;IAChD,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF,KACA;AAGC,MAAM,KAAK;AACX,MAAM,UAAU,SAAS,OAAO;AAGhC,MAAM,oBAAoB;IAC/B,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,UAAU;IACtB,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,yBAAyB,CAAC,CAAC;AACzC;AAGO,MAAM,eAAe;IAC1B,QAAQ,GAAG,CAAC;IACZ;IAEA,oCAAoC;IACpC,uCAAmC;;IAEnC,OAAO;QACL,QAAQ,GAAG,CAAC;IACd;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/hooks/use-auth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist, createJSONStorage } from \"zustand/middleware\";\r\nimport { User } from \"@/lib/types\";\r\nimport { supabase } from \"@/lib/supabase\";\r\nimport {\r\n  AuthError,\r\n  Session,\r\n  User as SupabaseUser,\r\n} from \"@supabase/supabase-js\";\r\nimport { useEffect } from \"react\";\r\n\r\ninterface AuthState {\r\n  user: User | null;\r\n  session: Session | null;\r\n  isAuthenticated: boolean;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n  // Auth methods\r\n  signUp: (\r\n    email: string,\r\n    password: string,\r\n    username: string\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signIn: (\r\n    email: string,\r\n    password: string\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signInWithOAuth: (\r\n    provider: \"google\" | \"azure\" | \"discord\" | \"facebook\"\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signOut: () => Promise<void>;\r\n  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;\r\n  updateProfile: (updates: Partial<User>) => Promise<{ error: Error | null }>;\r\n  uploadAvatar: (\r\n    file: File\r\n  ) => Promise<{ url: string | null; error: Error | null }>;\r\n  // Internal methods\r\n  setSession: (session: Session | null) => void;\r\n  setUser: (user: User | null) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  setError: (error: string | null) => void;\r\n}\r\n\r\nconst useAuthStore = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      user: null,\r\n      session: null,\r\n      isAuthenticated: false,\r\n      isLoading: true,\r\n      error: null,\r\n\r\n      signUp: async (email: string, password: string, username: string) => {\r\n        console.log(\"🚀 Starting signup process...\");\r\n        console.log(\"- Email:\", email);\r\n        console.log(\"- Username:\", username);\r\n        console.log(\"- Supabase client:\", !!supabase);\r\n\r\n        set({ isLoading: true, error: null });\r\n\r\n        try {\r\n          const { data, error } = await supabase.auth.signUp({\r\n            email,\r\n            password,\r\n            options: {\r\n              data: {\r\n                username,\r\n              },\r\n            },\r\n          });\r\n\r\n          console.log(\"📝 Signup response:\", { data, error });\r\n\r\n          if (error) {\r\n            console.error(\"❌ Signup error:\", error);\r\n            set({ error: error.message, isLoading: false });\r\n            return { error };\r\n          }\r\n\r\n          console.log(\"✅ Signup successful!\");\r\n          // User will be created in the database via trigger\r\n          set({ isLoading: false });\r\n          return { error: null };\r\n        } catch (err) {\r\n          console.error(\"💥 Signup exception:\", err);\r\n          const errorMessage =\r\n            err instanceof Error ? err.message : \"Unknown error\";\r\n          set({ error: errorMessage, isLoading: false });\r\n          return { error: { message: errorMessage } as any };\r\n        }\r\n      },\r\n\r\n      signIn: async (email: string, password: string) => {\r\n        set({ isLoading: true, error: null });\r\n\r\n        try {\r\n          const { data, error } = await supabase.auth.signInWithPassword({\r\n            email,\r\n            password,\r\n          });\r\n\r\n          if (error) {\r\n            set({ error: error.message, isLoading: false });\r\n            return { error };\r\n          }\r\n\r\n          // Session will be handled by the auth state change listener\r\n          // But we'll add a timeout to ensure loading doesn't get stuck\r\n          setTimeout(() => {\r\n            const currentState = get();\r\n            if (currentState.isLoading && !currentState.session) {\r\n              console.warn(\"Sign-in timeout, forcing loading to false\");\r\n              set({ isLoading: false });\r\n            }\r\n          }, 5000);\r\n\r\n          return { error: null };\r\n        } catch (err) {\r\n          const errorMessage =\r\n            err instanceof Error ? err.message : \"Sign-in failed\";\r\n          set({ error: errorMessage, isLoading: false });\r\n          return { error: { message: errorMessage } as any };\r\n        }\r\n      },\r\n\r\n      signInWithOAuth: async (\r\n        provider: \"google\" | \"azure\" | \"discord\" | \"facebook\"\r\n      ) => {\r\n        set({ isLoading: true, error: null });\r\n\r\n        const { data, error } = await supabase.auth.signInWithOAuth({\r\n          provider,\r\n          options: {\r\n            redirectTo: `${window.location.origin}/auth/callback`,\r\n          },\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error };\r\n        }\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      signOut: async () => {\r\n        set({ isLoading: true });\r\n        await supabase.auth.signOut();\r\n        set({\r\n          user: null,\r\n          session: null,\r\n          isAuthenticated: false,\r\n          isLoading: false,\r\n          error: null,\r\n        });\r\n      },\r\n\r\n      resetPassword: async (email: string) => {\r\n        const { error } = await supabase.auth.resetPasswordForEmail(email, {\r\n          redirectTo: `${window.location.origin}/auth/reset-password`,\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message });\r\n          return { error };\r\n        }\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      updateProfile: async (updates: Partial<User>) => {\r\n        const { user } = get();\r\n        if (!user) {\r\n          const error = new Error(\"No user logged in\");\r\n          set({ error: error.message });\r\n          return { error };\r\n        }\r\n\r\n        set({ isLoading: true, error: null });\r\n\r\n        // Map camelCase properties to snake_case database columns\r\n        const dbUpdates: any = {};\r\n\r\n        if (updates.avatarUrl !== undefined) {\r\n          dbUpdates.avatar_url = updates.avatarUrl;\r\n        }\r\n        if (updates.bio !== undefined) {\r\n          dbUpdates.bio = updates.bio;\r\n        }\r\n        if (updates.username !== undefined) {\r\n          dbUpdates.username = updates.username;\r\n        }\r\n        if (updates.email !== undefined) {\r\n          dbUpdates.email = updates.email;\r\n        }\r\n        if (updates.contacts?.website !== undefined) {\r\n          dbUpdates.website_url = updates.contacts.website.value;\r\n          dbUpdates.website_public = updates.contacts.website.isPublic;\r\n        }\r\n        if (updates.contacts?.phone !== undefined) {\r\n          dbUpdates.phone = updates.contacts.phone.value;\r\n          dbUpdates.phone_public = updates.contacts.phone.isPublic;\r\n        }\r\n        if (updates.contacts?.messaging !== undefined) {\r\n          dbUpdates.messaging_platform = updates.contacts.messaging.platform;\r\n          dbUpdates.messaging_username = updates.contacts.messaging.username;\r\n          dbUpdates.messaging_public = updates.contacts.messaging.isPublic;\r\n        }\r\n\r\n        const { error } = await supabase\r\n          .from(\"users\")\r\n          .update(dbUpdates)\r\n          .eq(\"id\", user.id);\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error: new Error(error.message) };\r\n        }\r\n\r\n        // Update local user state\r\n        set({\r\n          user: { ...user, ...updates },\r\n          isLoading: false,\r\n        });\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      uploadAvatar: async (file: File) => {\r\n        const { user } = get();\r\n        if (!user) {\r\n          const error = new Error(\"No user logged in\");\r\n          return { url: null, error };\r\n        }\r\n\r\n        const fileExt = file.name.split(\".\").pop();\r\n        const fileName = `${user.id}/avatar.${fileExt}`;\r\n\r\n        const { error: uploadError } = await supabase.storage\r\n          .from(\"avatars\")\r\n          .upload(fileName, file, { upsert: true });\r\n\r\n        if (uploadError) {\r\n          return { url: null, error: new Error(uploadError.message) };\r\n        }\r\n\r\n        const { data } = supabase.storage\r\n          .from(\"avatars\")\r\n          .getPublicUrl(fileName);\r\n\r\n        const avatarUrl = data.publicUrl;\r\n\r\n        // Update user profile with new avatar URL\r\n        const { error: updateError } = await get().updateProfile({ avatarUrl });\r\n\r\n        if (updateError) {\r\n          return { url: null, error: updateError };\r\n        }\r\n\r\n        return { url: avatarUrl, error: null };\r\n      },\r\n\r\n      setSession: (session: Session | null) => {\r\n        set({ session, isAuthenticated: !!session });\r\n      },\r\n\r\n      setUser: (user: User | null) => {\r\n        set({ user, isAuthenticated: !!user, isLoading: false });\r\n      },\r\n\r\n      setLoading: (loading: boolean) => {\r\n        set({ isLoading: loading });\r\n      },\r\n\r\n      setError: (error: string | null) => {\r\n        set({ error });\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n      storage: createJSONStorage(() => localStorage),\r\n      partialize: (state) => ({\r\n        // Only persist user data, not session (Supabase handles session persistence)\r\n        user: state.user,\r\n        // Don't persist loading states to avoid stuck states\r\n      }),\r\n      // Reset loading state on hydration\r\n      onRehydrateStorage: () => (state) => {\r\n        if (state) {\r\n          state.isLoading = true; // Will be set to false by auth initialization\r\n          state.isAuthenticated = !!state.user;\r\n        }\r\n      },\r\n    }\r\n  )\r\n);\r\n\r\n// Helper function to fetch user profile from database\r\nconst fetchUserProfile = async (\r\n  supabaseUser: SupabaseUser\r\n): Promise<User | null> => {\r\n  console.log(\"🔍 Fetching user profile for:\", supabaseUser.id);\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(\"users\")\r\n      .select(\"*\")\r\n      .eq(\"id\", supabaseUser.id)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching user profile:\", error);\r\n\r\n      // If user doesn't exist in users table, try to create one\r\n      if (error.code === \"PGRST116\") {\r\n        console.log(\r\n          \"🔄 User not found in users table, attempting to create...\"\r\n        );\r\n        return await createUserProfile(supabaseUser);\r\n      }\r\n\r\n      return null;\r\n    }\r\n\r\n    console.log(\"✅ User profile fetched successfully:\", data.username);\r\n\r\n    return {\r\n      id: data.id,\r\n      username: data.username,\r\n      email: data.email,\r\n      avatarUrl: data.avatar_url,\r\n      bio: data.bio,\r\n      contacts: {\r\n        website: {\r\n          value: data.website_url || \"\",\r\n          isPublic: data.website_public || false,\r\n        },\r\n        phone: {\r\n          value: data.phone || \"\",\r\n          isPublic: data.phone_public || false,\r\n        },\r\n        messaging: {\r\n          platform: data.messaging_platform || \"\",\r\n          username: data.messaging_username || \"\",\r\n          isPublic: data.messaging_public || false,\r\n        },\r\n      },\r\n    };\r\n  } catch (err) {\r\n    console.error(\"Exception in fetchUserProfile:\", err);\r\n    return null;\r\n  }\r\n};\r\n\r\n// Helper function to create a user profile when one doesn't exist\r\nconst createUserProfile = async (\r\n  supabaseUser: SupabaseUser\r\n): Promise<User | null> => {\r\n  try {\r\n    const username =\r\n      supabaseUser.user_metadata?.username ||\r\n      supabaseUser.email?.split(\"@\")[0] ||\r\n      \"user\";\r\n\r\n    console.log(\"📝 Creating user profile for:\", {\r\n      id: supabaseUser.id,\r\n      email: supabaseUser.email,\r\n      username: username,\r\n    });\r\n\r\n    const { data, error } = await supabase\r\n      .from(\"users\")\r\n      .insert({\r\n        id: supabaseUser.id,\r\n        username: username,\r\n        email: supabaseUser.email,\r\n      })\r\n      .select()\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(\"❌ Failed to create user profile:\", error);\r\n\r\n      // Return a minimal user object to prevent app crashes\r\n      return {\r\n        id: supabaseUser.id,\r\n        username: username,\r\n        email: supabaseUser.email || \"\",\r\n        contacts: {\r\n          website: { value: \"\", isPublic: false },\r\n          phone: { value: \"\", isPublic: false },\r\n          messaging: { platform: \"\", username: \"\", isPublic: false },\r\n        },\r\n      };\r\n    }\r\n\r\n    console.log(\"✅ User profile created successfully:\", data);\r\n\r\n    return {\r\n      id: data.id,\r\n      username: data.username,\r\n      email: data.email,\r\n      avatarUrl: data.avatar_url,\r\n      bio: data.bio,\r\n      contacts: {\r\n        website: {\r\n          value: data.website_url || \"\",\r\n          isPublic: data.website_public || false,\r\n        },\r\n        phone: {\r\n          value: data.phone || \"\",\r\n          isPublic: data.phone_public || false,\r\n        },\r\n        messaging: {\r\n          platform: data.messaging_platform || \"\",\r\n          username: data.messaging_username || \"\",\r\n          isPublic: data.messaging_public || false,\r\n        },\r\n      },\r\n    };\r\n  } catch (err) {\r\n    console.error(\"💥 Exception in createUserProfile:\", err);\r\n\r\n    // Return a minimal user object as last resort\r\n    return {\r\n      id: supabaseUser.id,\r\n      username:\r\n        supabaseUser.user_metadata?.username ||\r\n        supabaseUser.email?.split(\"@\")[0] ||\r\n        \"user\",\r\n      email: supabaseUser.email || \"\",\r\n      contacts: {\r\n        website: { value: \"\", isPublic: false },\r\n        phone: { value: \"\", isPublic: false },\r\n        messaging: { platform: \"\", username: \"\", isPublic: false },\r\n      },\r\n    };\r\n  }\r\n};\r\n\r\n// Custom hook to initialize and use the store\r\nexport const useAuth = () => {\r\n  const state = useAuthStore();\r\n\r\n  useEffect(() => {\r\n    let isInitialized = false;\r\n    let timeoutId: NodeJS.Timeout;\r\n\r\n    // Add timeout to prevent infinite loading (only if not already authenticated)\r\n    if (!useAuthStore.getState().isAuthenticated) {\r\n      timeoutId = setTimeout(() => {\r\n        console.warn(\r\n          \"⏰ Auth initialization timeout, forcing loading to false\"\r\n        );\r\n        useAuthStore.getState().setLoading(false);\r\n      }, 10000); // Increased to 10 seconds and only runs if not authenticated\r\n    }\r\n\r\n    // Listen for auth changes (handles sign in/out events after initialization)\r\n    const {\r\n      data: { subscription },\r\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\r\n      console.log(\"Auth state changed:\", event, session);\r\n\r\n      // Skip INITIAL_SESSION event to avoid duplicate processing\r\n      if (event === \"INITIAL_SESSION\") {\r\n        console.log(\r\n          \"⏭️ Skipping INITIAL_SESSION event (handled by initializeAuth)\"\r\n        );\r\n        return;\r\n      }\r\n\r\n      if (timeoutId) clearTimeout(timeoutId); // Clear timeout when auth state changes\r\n\r\n      if (session?.user) {\r\n        console.log(\"👤 Session found, fetching user profile...\");\r\n        const userProfile = await fetchUserProfile(session.user);\r\n        useAuthStore.getState().setSession(session);\r\n        useAuthStore.getState().setUser(userProfile);\r\n      } else {\r\n        console.log(\"❌ No session found\");\r\n        useAuthStore.getState().setSession(null);\r\n        useAuthStore.getState().setUser(null);\r\n        useAuthStore.getState().setLoading(false);\r\n      }\r\n    });\r\n\r\n    // Initialize auth state\r\n    const initializeAuth = async () => {\r\n      console.log(\"🚀 Initializing auth...\");\r\n      try {\r\n        const {\r\n          data: { session },\r\n        } = await supabase.auth.getSession();\r\n\r\n        if (session?.user) {\r\n          console.log(\"👤 Initial session found, fetching user profile...\");\r\n          const userProfile = await fetchUserProfile(session.user);\r\n          useAuthStore.getState().setSession(session);\r\n          useAuthStore.getState().setUser(userProfile);\r\n        } else {\r\n          console.log(\"❌ No initial session found\");\r\n          useAuthStore.getState().setSession(null);\r\n          useAuthStore.getState().setUser(null);\r\n          useAuthStore.getState().setLoading(false);\r\n        }\r\n\r\n        isInitialized = true;\r\n        console.log(\"✅ Auth initialization complete\");\r\n        if (timeoutId) clearTimeout(timeoutId);\r\n      } catch (error) {\r\n        console.error(\"💥 Error initializing auth:\", error);\r\n        useAuthStore.getState().setLoading(false);\r\n        isInitialized = true;\r\n        if (timeoutId) clearTimeout(timeoutId);\r\n      }\r\n    };\r\n\r\n    initializeAuth();\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n      subscription.unsubscribe();\r\n    };\r\n  }, []);\r\n\r\n  return state;\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AAMA;AAXA;;;;;AA6CA,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,IACxB,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,QAAQ,OAAO,OAAe,UAAkB;YAC9C,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,YAAY;YACxB,QAAQ,GAAG,CAAC,eAAe;YAC3B,QAAQ,GAAG,CAAC,sBAAsB,CAAC,CAAC,sHAAA,CAAA,WAAQ;YAE5C,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;oBACjD;oBACA;oBACA,SAAS;wBACP,MAAM;4BACJ;wBACF;oBACF;gBACF;gBAEA,QAAQ,GAAG,CAAC,uBAAuB;oBAAE;oBAAM;gBAAM;gBAEjD,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,mBAAmB;oBACjC,IAAI;wBAAE,OAAO,MAAM,OAAO;wBAAE,WAAW;oBAAM;oBAC7C,OAAO;wBAAE;oBAAM;gBACjB;gBAEA,QAAQ,GAAG,CAAC;gBACZ,mDAAmD;gBACnD,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;gBACvC,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,OAAO;oBAAE,OAAO;wBAAE,SAAS;oBAAa;gBAAS;YACnD;QACF;QAEA,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;oBAC7D;oBACA;gBACF;gBAEA,IAAI,OAAO;oBACT,IAAI;wBAAE,OAAO,MAAM,OAAO;wBAAE,WAAW;oBAAM;oBAC7C,OAAO;wBAAE;oBAAM;gBACjB;gBAEA,4DAA4D;gBAC5D,8DAA8D;gBAC9D,WAAW;oBACT,MAAM,eAAe;oBACrB,IAAI,aAAa,SAAS,IAAI,CAAC,aAAa,OAAO,EAAE;wBACnD,QAAQ,IAAI,CAAC;wBACb,IAAI;4BAAE,WAAW;wBAAM;oBACzB;gBACF,GAAG;gBAEH,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,KAAK;gBACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;gBACvC,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,OAAO;oBAAE,OAAO;wBAAE,SAAS;oBAAa;gBAAS;YACnD;QACF;QAEA,iBAAiB,OACf;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBAC1D;gBACA,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBACvD;YACF;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,SAAS;YACP,IAAI;gBAAE,WAAW;YAAK;YACtB,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC3B,IAAI;gBACF,MAAM;gBACN,SAAS;gBACT,iBAAiB;gBACjB,WAAW;gBACX,OAAO;YACT;QACF;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC;gBAC3B,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;gBACT,MAAM,QAAQ,IAAI,MAAM;gBACxB,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC;gBAC3B,OAAO;oBAAE;gBAAM;YACjB;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,0DAA0D;YAC1D,MAAM,YAAiB,CAAC;YAExB,IAAI,QAAQ,SAAS,KAAK,WAAW;gBACnC,UAAU,UAAU,GAAG,QAAQ,SAAS;YAC1C;YACA,IAAI,QAAQ,GAAG,KAAK,WAAW;gBAC7B,UAAU,GAAG,GAAG,QAAQ,GAAG;YAC7B;YACA,IAAI,QAAQ,QAAQ,KAAK,WAAW;gBAClC,UAAU,QAAQ,GAAG,QAAQ,QAAQ;YACvC;YACA,IAAI,QAAQ,KAAK,KAAK,WAAW;gBAC/B,UAAU,KAAK,GAAG,QAAQ,KAAK;YACjC;YACA,IAAI,QAAQ,QAAQ,EAAE,YAAY,WAAW;gBAC3C,UAAU,WAAW,GAAG,QAAQ,QAAQ,CAAC,OAAO,CAAC,KAAK;gBACtD,UAAU,cAAc,GAAG,QAAQ,QAAQ,CAAC,OAAO,CAAC,QAAQ;YAC9D;YACA,IAAI,QAAQ,QAAQ,EAAE,UAAU,WAAW;gBACzC,UAAU,KAAK,GAAG,QAAQ,QAAQ,CAAC,KAAK,CAAC,KAAK;gBAC9C,UAAU,YAAY,GAAG,QAAQ,QAAQ,CAAC,KAAK,CAAC,QAAQ;YAC1D;YACA,IAAI,QAAQ,QAAQ,EAAE,cAAc,WAAW;gBAC7C,UAAU,kBAAkB,GAAG,QAAQ,QAAQ,CAAC,SAAS,CAAC,QAAQ;gBAClE,UAAU,kBAAkB,GAAG,QAAQ,QAAQ,CAAC,SAAS,CAAC,QAAQ;gBAClE,UAAU,gBAAgB,GAAG,QAAQ,QAAQ,CAAC,SAAS,CAAC,QAAQ;YAClE;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE,OAAO,IAAI,MAAM,MAAM,OAAO;gBAAE;YAC3C;YAEA,0BAA0B;YAC1B,IAAI;gBACF,MAAM;oBAAE,GAAG,IAAI;oBAAE,GAAG,OAAO;gBAAC;gBAC5B,WAAW;YACb;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,cAAc,OAAO;YACnB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;gBACT,MAAM,QAAQ,IAAI,MAAM;gBACxB,OAAO;oBAAE,KAAK;oBAAM;gBAAM;YAC5B;YAEA,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACxC,MAAM,WAAW,GAAG,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS;YAE/C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,WACL,MAAM,CAAC,UAAU,MAAM;gBAAE,QAAQ;YAAK;YAEzC,IAAI,aAAa;gBACf,OAAO;oBAAE,KAAK;oBAAM,OAAO,IAAI,MAAM,YAAY,OAAO;gBAAE;YAC5D;YAEA,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,WACL,YAAY,CAAC;YAEhB,MAAM,YAAY,KAAK,SAAS;YAEhC,0CAA0C;YAC1C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,MAAM,aAAa,CAAC;gBAAE;YAAU;YAErE,IAAI,aAAa;gBACf,OAAO;oBAAE,KAAK;oBAAM,OAAO;gBAAY;YACzC;YAEA,OAAO;gBAAE,KAAK;gBAAW,OAAO;YAAK;QACvC;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;gBAAS,iBAAiB,CAAC,CAAC;YAAQ;QAC5C;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;gBAAM,WAAW;YAAM;QACxD;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YACtB,6EAA6E;YAC7E,MAAM,MAAM,IAAI;QAElB,CAAC;IACD,mCAAmC;IACnC,oBAAoB,IAAM,CAAC;YACzB,IAAI,OAAO;gBACT,MAAM,SAAS,GAAG,MAAM,8CAA8C;gBACtE,MAAM,eAAe,GAAG,CAAC,CAAC,MAAM,IAAI;YACtC;QACF;AACF;AAIJ,sDAAsD;AACtD,MAAM,mBAAmB,OACvB;IAEA,QAAQ,GAAG,CAAC,iCAAiC,aAAa,EAAE;IAC5D,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAE9C,0DAA0D;YAC1D,IAAI,MAAM,IAAI,KAAK,YAAY;gBAC7B,QAAQ,GAAG,CACT;gBAEF,OAAO,MAAM,kBAAkB;YACjC;YAEA,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,wCAAwC,KAAK,QAAQ;QAEjE,OAAO;YACL,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,UAAU;YAC1B,KAAK,KAAK,GAAG;YACb,UAAU;gBACR,SAAS;oBACP,OAAO,KAAK,WAAW,IAAI;oBAC3B,UAAU,KAAK,cAAc,IAAI;gBACnC;gBACA,OAAO;oBACL,OAAO,KAAK,KAAK,IAAI;oBACrB,UAAU,KAAK,YAAY,IAAI;gBACjC;gBACA,WAAW;oBACT,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,gBAAgB,IAAI;gBACrC;YACF;QACF;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAEA,kEAAkE;AAClE,MAAM,oBAAoB,OACxB;IAEA,IAAI;QACF,MAAM,WACJ,aAAa,aAAa,EAAE,YAC5B,aAAa,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IACjC;QAEF,QAAQ,GAAG,CAAC,iCAAiC;YAC3C,IAAI,aAAa,EAAE;YACnB,OAAO,aAAa,KAAK;YACzB,UAAU;QACZ;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;YACN,IAAI,aAAa,EAAE;YACnB,UAAU;YACV,OAAO,aAAa,KAAK;QAC3B,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAElD,sDAAsD;YACtD,OAAO;gBACL,IAAI,aAAa,EAAE;gBACnB,UAAU;gBACV,OAAO,aAAa,KAAK,IAAI;gBAC7B,UAAU;oBACR,SAAS;wBAAE,OAAO;wBAAI,UAAU;oBAAM;oBACtC,OAAO;wBAAE,OAAO;wBAAI,UAAU;oBAAM;oBACpC,WAAW;wBAAE,UAAU;wBAAI,UAAU;wBAAI,UAAU;oBAAM;gBAC3D;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,wCAAwC;QAEpD,OAAO;YACL,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,UAAU;YAC1B,KAAK,KAAK,GAAG;YACb,UAAU;gBACR,SAAS;oBACP,OAAO,KAAK,WAAW,IAAI;oBAC3B,UAAU,KAAK,cAAc,IAAI;gBACnC;gBACA,OAAO;oBACL,OAAO,KAAK,KAAK,IAAI;oBACrB,UAAU,KAAK,YAAY,IAAI;gBACjC;gBACA,WAAW;oBACT,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,gBAAgB,IAAI;gBACrC;YACF;QACF;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,sCAAsC;QAEpD,8CAA8C;QAC9C,OAAO;YACL,IAAI,aAAa,EAAE;YACnB,UACE,aAAa,aAAa,EAAE,YAC5B,aAAa,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IACjC;YACF,OAAO,aAAa,KAAK,IAAI;YAC7B,UAAU;gBACR,SAAS;oBAAE,OAAO;oBAAI,UAAU;gBAAM;gBACtC,OAAO;oBAAE,OAAO;oBAAI,UAAU;gBAAM;gBACpC,WAAW;oBAAE,UAAU;oBAAI,UAAU;oBAAI,UAAU;gBAAM;YAC3D;QACF;IACF;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,QAAQ;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;QACpB,IAAI;QAEJ,8EAA8E;QAC9E,IAAI,CAAC,aAAa,QAAQ,GAAG,eAAe,EAAE;YAC5C,YAAY,WAAW;gBACrB,QAAQ,IAAI,CACV;gBAEF,aAAa,QAAQ,GAAG,UAAU,CAAC;YACrC,GAAG,QAAQ,6DAA6D;QAC1E;QAEA,4EAA4E;QAC5E,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;YAChD,QAAQ,GAAG,CAAC,uBAAuB,OAAO;YAE1C,2DAA2D;YAC3D,IAAI,UAAU,mBAAmB;gBAC/B,QAAQ,GAAG,CACT;gBAEF;YACF;YAEA,IAAI,WAAW,aAAa,YAAY,wCAAwC;YAEhF,IAAI,SAAS,MAAM;gBACjB,QAAQ,GAAG,CAAC;gBACZ,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;gBACvD,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;YAClC,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;gBAChC,aAAa,QAAQ,GAAG,UAAU,CAAC;YACrC;QACF;QAEA,wBAAwB;QACxB,MAAM,iBAAiB;YACrB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;gBAElC,IAAI,SAAS,MAAM;oBACjB,QAAQ,GAAG,CAAC;oBACZ,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;oBACvD,aAAa,QAAQ,GAAG,UAAU,CAAC;oBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;gBAClC,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,aAAa,QAAQ,GAAG,UAAU,CAAC;oBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;oBAChC,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACrC;gBAEA,gBAAgB;gBAChB,QAAQ,GAAG,CAAC;gBACZ,IAAI,WAAW,aAAa;YAC9B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,gBAAgB;gBAChB,IAAI,WAAW,aAAa;YAC9B;QACF;QAEA;QAEA,OAAO;YACL,IAAI,WAAW,aAAa;YAC5B,aAAa,WAAW;QAC1B;IACF,GAAG,EAAE;IAEL,OAAO;AACT", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState, formState } = useFormContext()\r\n\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nconst FormItem = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  )\r\n})\r\nFormItem.displayName = \"FormItem\"\r\n\r\nconst FormLabel = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      ref={ref}\r\n      className={cn(error && \"text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormLabel.displayName = \"FormLabel\"\r\n\r\nconst FormControl = React.forwardRef<\r\n  React.ElementRef<typeof Slot>,\r\n  React.ComponentPropsWithoutRef<typeof Slot>\r\n>(({ ...props }, ref) => {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      ref={ref}\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormControl.displayName = \"FormControl\"\r\n\r\nconst FormDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formDescriptionId}\r\n      className={cn(\"text-sm text-muted-foreground\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormDescription.displayName = \"FormDescription\"\r\n\r\nconst FormMessage = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, children, ...props }, ref) => {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formMessageId}\r\n      className={cn(\"text-sm font-medium text-destructive\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n})\r\nFormMessage.displayName = \"FormMessage\"\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AASA;AACA;AAfA;;;;;;;AAiBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB;QAC3C,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,GAAG,OAAO,EAAE;IACf,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,KAAK;QACL,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM;IAEpD,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/app/login/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { zod<PERSON><PERSON>olver } from \"@hookform/resolvers/zod\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { useAuth } from \"@/hooks/use-auth\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport {\r\n  Form,\r\n  FormField,\r\n  FormItem,\r\n  FormControl,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Eye, EyeOff, Mail, AlertCircle, CheckCircle } from \"lucide-react\";\r\n\r\nconst loginSchema = z.object({\r\n  email: z.string().email({ message: \"Please enter a valid email address.\" }),\r\n  password: z.string().min(1, { message: \"Password is required.\" }),\r\n});\r\n\r\nexport default function LoginPage() {\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const { signIn, signInWithOAuth, isLoading, error } = useAuth();\r\n  const { toast } = useToast();\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [oauthLoading, setOauthLoading] = useState<string | null>(null);\r\n  const [localLoading, setLocalLoading] = useState(false);\r\n\r\n  const form = useForm<z.infer<typeof loginSchema>>({\r\n    resolver: zodResolver(loginSchema),\r\n    defaultValues: {\r\n      email: \"\",\r\n      password: \"\",\r\n    },\r\n  });\r\n\r\n  // Handle URL messages\r\n  useEffect(() => {\r\n    const message = searchParams.get(\"message\");\r\n    const errorParam = searchParams.get(\"error\");\r\n\r\n    if (message === \"check_email\") {\r\n      toast({\r\n        title: \"Check Your Email\",\r\n        description:\r\n          \"We sent you a verification link. Please check your email and click the link to verify your account.\",\r\n      });\r\n    }\r\n\r\n    if (errorParam) {\r\n      toast({\r\n        title: \"Authentication Error\",\r\n        description:\r\n          \"There was an error during authentication. Please try again.\",\r\n        variant: \"destructive\",\r\n      });\r\n    }\r\n  }, [searchParams, toast]);\r\n\r\n  const onSubmit = async (values: z.infer<typeof loginSchema>) => {\r\n    setLocalLoading(true);\r\n\r\n    try {\r\n      const { error } = await signIn(values.email, values.password);\r\n\r\n      if (error) {\r\n        toast({\r\n          title: \"Login Failed\",\r\n          description: error.message,\r\n          variant: \"destructive\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      toast({\r\n        title: \"Login Successful\",\r\n        description: \"Welcome back!\",\r\n      });\r\n      router.push(\"/\");\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleOAuthLogin = async (\r\n    provider: \"google\" | \"azure\" | \"discord\" | \"facebook\"\r\n  ) => {\r\n    setOauthLoading(provider);\r\n    const { error } = await signInWithOAuth(provider);\r\n\r\n    if (error) {\r\n      toast({\r\n        title: \"OAuth Login Failed\",\r\n        description: error.message,\r\n        variant: \"destructive\",\r\n      });\r\n    }\r\n\r\n    setOauthLoading(null);\r\n  };\r\n\r\n  return (\r\n    <main className=\"flex items-center justify-center min-h-screen bg-background p-4\">\r\n      <div className=\"w-full max-w-md\">\r\n        <Form {...form}>\r\n          <form onSubmit={form.handleSubmit(onSubmit)}>\r\n            <Card className=\"shadow-2xl\">\r\n              <CardHeader className=\"text-center\">\r\n                <CardTitle className=\"text-3xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent\">\r\n                  FireFrame\r\n                </CardTitle>\r\n                <CardDescription>\r\n                  Enter your credentials to access your account\r\n                </CardDescription>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-4\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"email\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <Label htmlFor=\"email\">Email</Label>\r\n                      <FormControl>\r\n                        <Input\r\n                          id=\"email\"\r\n                          type=\"email\"\r\n                          autoComplete=\"email\"\r\n                          placeholder=\"<EMAIL>\"\r\n                          {...field}\r\n                        />\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"password\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <Label htmlFor=\"password\">Password</Label>\r\n                      <FormControl>\r\n                        <div className=\"relative\">\r\n                          <Input\r\n                            id=\"password\"\r\n                            type={showPassword ? \"text\" : \"password\"}\r\n                            autoComplete=\"current-password\"\r\n                            placeholder=\"••••••••\"\r\n                            {...field}\r\n                          />\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"ghost\"\r\n                            size=\"sm\"\r\n                            className=\"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\"\r\n                            onClick={() => setShowPassword(!showPassword)}\r\n                          >\r\n                            {showPassword ? (\r\n                              <EyeOff className=\"h-4 w-4\" />\r\n                            ) : (\r\n                              <Eye className=\"h-4 w-4\" />\r\n                            )}\r\n                          </Button>\r\n                        </div>\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <div className=\"text-right\">\r\n                  <Link\r\n                    href=\"/auth/forgot-password\"\r\n                    className=\"text-sm text-primary hover:underline\"\r\n                  >\r\n                    Forgot password?\r\n                  </Link>\r\n                </div>\r\n              </CardContent>\r\n              <CardFooter className=\"flex flex-col gap-4\">\r\n                {error && (\r\n                  <Alert variant=\"destructive\">\r\n                    <AlertCircle className=\"h-4 w-4\" />\r\n                    <AlertDescription>{error}</AlertDescription>\r\n                  </Alert>\r\n                )}\r\n\r\n                <Button\r\n                  type=\"submit\"\r\n                  className=\"w-full font-bold\"\r\n                  disabled={localLoading || isLoading}\r\n                >\r\n                  {localLoading || isLoading ? \"Signing In...\" : \"Sign In\"}\r\n                </Button>\r\n\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-0 flex items-center\">\r\n                    <Separator className=\"w-full\" />\r\n                  </div>\r\n                  <div className=\"relative flex justify-center text-xs uppercase\">\r\n                    <span className=\"bg-background px-2 text-muted-foreground\">\r\n                      Or continue with\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-2 gap-3\">\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"outline\"\r\n                    onClick={() => handleOAuthLogin(\"google\")}\r\n                    disabled={oauthLoading === \"google\"}\r\n                    className=\"w-full\"\r\n                  >\r\n                    {oauthLoading === \"google\" ? (\r\n                      <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n                    ) : (\r\n                      <svg className=\"h-4 w-4\" viewBox=\"0 0 24 24\">\r\n                        <path\r\n                          fill=\"currentColor\"\r\n                          d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\r\n                        />\r\n                        <path\r\n                          fill=\"currentColor\"\r\n                          d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\r\n                        />\r\n                        <path\r\n                          fill=\"currentColor\"\r\n                          d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\r\n                        />\r\n                        <path\r\n                          fill=\"currentColor\"\r\n                          d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\r\n                        />\r\n                      </svg>\r\n                    )}\r\n                    Google\r\n                  </Button>\r\n\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"outline\"\r\n                    onClick={() => handleOAuthLogin(\"discord\")}\r\n                    disabled={oauthLoading === \"discord\"}\r\n                    className=\"w-full\"\r\n                  >\r\n                    {oauthLoading === \"discord\" ? (\r\n                      <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n                    ) : (\r\n                      <svg\r\n                        className=\"h-4 w-4\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"currentColor\"\r\n                      >\r\n                        <path d=\"M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z\" />\r\n                      </svg>\r\n                    )}\r\n                    Discord\r\n                  </Button>\r\n\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"outline\"\r\n                    onClick={() => handleOAuthLogin(\"azure\")}\r\n                    disabled={oauthLoading === \"azure\"}\r\n                    className=\"w-full\"\r\n                  >\r\n                    {oauthLoading === \"azure\" ? (\r\n                      <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n                    ) : (\r\n                      <svg\r\n                        className=\"h-4 w-4\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"currentColor\"\r\n                      >\r\n                        <path d=\"M13.5 3L8.5 21h3.75L17.25 3H13.5zM12 3L6 21h3.75L15.75 3H12z\" />\r\n                      </svg>\r\n                    )}\r\n                    Azure\r\n                  </Button>\r\n\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"outline\"\r\n                    onClick={() => handleOAuthLogin(\"facebook\")}\r\n                    disabled={oauthLoading === \"facebook\"}\r\n                    className=\"w-full\"\r\n                  >\r\n                    {oauthLoading === \"facebook\" ? (\r\n                      <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n                    ) : (\r\n                      <svg\r\n                        className=\"h-4 w-4\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"currentColor\"\r\n                      >\r\n                        <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\" />\r\n                      </svg>\r\n                    )}\r\n                    Facebook\r\n                  </Button>\r\n                </div>\r\n\r\n                <p className=\"text-sm text-center text-muted-foreground\">\r\n                  Don&apos;t have an account?{\" \"}\r\n                  <Link\r\n                    href=\"/signup\"\r\n                    className=\"font-semibold text-primary hover:underline\"\r\n                  >\r\n                    Sign up\r\n                  </Link>\r\n                </p>\r\n              </CardFooter>\r\n            </Card>\r\n          </form>\r\n        </Form>\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AAAA;AAAA;AA9BA;;;;;;;;;;;;;;;;;;AAgCA,MAAM,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QAAE,SAAS;IAAsC;IACzE,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAAwB;AACjE;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC5D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA+B;QAChD,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,UAAU;QACZ;IACF;IAEA,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,MAAM,aAAa,aAAa,GAAG,CAAC;QAEpC,IAAI,YAAY,eAAe;YAC7B,MAAM;gBACJ,OAAO;gBACP,aACE;YACJ;QACF;QAEA,IAAI,YAAY;YACd,MAAM;gBACJ,OAAO;gBACP,aACE;gBACF,SAAS;YACX;QACF;IACF,GAAG;QAAC;QAAc;KAAM;IAExB,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO,KAAK,EAAE,OAAO,QAAQ;YAE5D,IAAI,OAAO;gBACT,MAAM;oBACJ,OAAO;oBACP,aAAa,MAAM,OAAO;oBAC1B,SAAS;gBACX;gBACA;YACF;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YACA,OAAO,IAAI,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB,OACvB;QAEA,gBAAgB;QAChB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,gBAAgB;QAExC,IAAI,OAAO;YACT,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO;gBAC1B,SAAS;YACX;QACF;QAEA,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBAAK,UAAU,KAAK,YAAY,CAAC;8BAChC,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA0G;;;;;;kDAG/H,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,cAAa;4DACb,aAAY;4DACX,GAAG,KAAK;;;;;;;;;;;kEAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAIlB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAM,eAAe,SAAS;oEAC9B,cAAa;oEACb,aAAY;oEACX,GAAG,KAAK;;;;;;8EAEX,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,gBAAgB,CAAC;8EAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;+FAElB,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kEAKvB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAKL,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;oCACnB,uBACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;0DACb,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC,iIAAA,CAAA,mBAAgB;0DAAE;;;;;;;;;;;;kDAIvB,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,UAAU,gBAAgB;kDAEzB,gBAAgB,YAAY,kBAAkB;;;;;;kDAGjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,qIAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;;;;;;;;;;;;kDAM/D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,iBAAiB;gDAChC,UAAU,iBAAiB;gDAC3B,WAAU;;oDAET,iBAAiB,yBAChB,8OAAC;wDAAI,WAAU;;;;;6EAEf,8OAAC;wDAAI,WAAU;wDAAU,SAAQ;;0EAC/B,8OAAC;gEACC,MAAK;gEACL,GAAE;;;;;;0EAEJ,8OAAC;gEACC,MAAK;gEACL,GAAE;;;;;;0EAEJ,8OAAC;gEACC,MAAK;gEACL,GAAE;;;;;;0EAEJ,8OAAC;gEACC,MAAK;gEACL,GAAE;;;;;;;;;;;;oDAGN;;;;;;;0DAIJ,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,iBAAiB;gDAChC,UAAU,iBAAiB;gDAC3B,WAAU;;oDAET,iBAAiB,0BAChB,8OAAC;wDAAI,WAAU;;;;;6EAEf,8OAAC;wDACC,WAAU;wDACV,SAAQ;wDACR,MAAK;kEAEL,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;oDAEV;;;;;;;0DAIJ,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,iBAAiB;gDAChC,UAAU,iBAAiB;gDAC3B,WAAU;;oDAET,iBAAiB,wBAChB,8OAAC;wDAAI,WAAU;;;;;6EAEf,8OAAC;wDACC,WAAU;wDACV,SAAQ;wDACR,MAAK;kEAEL,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;oDAEV;;;;;;;0DAIJ,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,iBAAiB;gDAChC,UAAU,iBAAiB;gDAC3B,WAAU;;oDAET,iBAAiB,2BAChB,8OAAC;wDAAI,WAAU;;;;;6EAEf,8OAAC;wDACC,WAAU;wDACV,SAAQ;wDACR,MAAK;kEAEL,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;oDAEV;;;;;;;;;;;;;kDAKN,8OAAC;wCAAE,WAAU;;4CAA4C;4CAC3B;0DAC5B,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}