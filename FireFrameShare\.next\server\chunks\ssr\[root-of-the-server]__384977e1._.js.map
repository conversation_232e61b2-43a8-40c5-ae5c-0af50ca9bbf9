{"version": 3, "sources": [], "sections": [{"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from \"@supabase/supabase-js\";\n\n// Supabase configuration with fallback values for debugging\nconst supabaseUrl =\n  process.env.NEXT_PUBLIC_SUPABASE_URL;\nconst supabaseAnonKey =\n  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n\n// Debug environment variables (remove after testing)\nconsole.log(\"🔍 Environment Debug:\");\nconsole.log(\"- NODE_ENV:\", process.env.NODE_ENV);\nconsole.log(\n  \"- URL from env:\",\n  process.env.NEXT_PUBLIC_SUPABASE_URL ? \"✅ Loaded\" : \"❌ Missing\"\n);\nconsole.log(\n  \"- Anon Key from env:\",\n  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? \"✅ Loaded\" : \"❌ Missing\"\n);\nconsole.log(\"- Final URL:\", supabaseUrl);\nconsole.log(\n  \"- Final Anon Key (first 20 chars):\",\n  supabaseAnonKey?.substring(0, 20) + \"...\"\n);\n\n// Validate final values\nif (!supabaseUrl) {\n  throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\n\nif (!supabaseAnonKey) {\n  throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\n\n// Create Supabase client for client-side operations\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n  },\n  realtime: {\n    params: {\n      eventsPerSecond: 10,\n    },\n  },\n});\n\n// Create Supabase client with service role for server-side operations\n// Only use this for server-side operations that require elevated permissions\nexport const supabaseAdmin =\n  typeof window === \"undefined\" && supabaseServiceRoleKey\n    ? createClient(supabaseUrl, supabaseServiceRoleKey, {\n        auth: {\n          autoRefreshToken: false,\n          persistSession: false,\n        },\n      })\n    : null;\n\n// Database and Storage references for convenience\nexport const db = supabase;\nexport const storage = supabase.storage;\n\n// Log configuration (without sensitive data)\nexport const logSupabaseConfig = () => {\n  console.log(\"Supabase Configuration:\");\n  console.log(\"- URL:\", supabaseUrl);\n  console.log(\"- Environment:\", process.env.NODE_ENV);\n  console.log(\"- Client initialized:\", !!supabase);\n};\n\n// Initialize Supabase debugging\nexport const initSupabase = () => {\n  console.log(\"🚀 Supabase initialized\");\n  logSupabaseConfig();\n\n  // Test connection on initialization\n  if (typeof window !== \"undefined\") {\n    console.log(\"Client-side Supabase ready\");\n  } else {\n    console.log(\"Server-side Supabase ready\");\n  }\n};\n\nexport default supabase;\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA,4DAA4D;AAC5D,MAAM;AAEN,MAAM;AAEN,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB;AAEpE,qDAAqD;AACrD,QAAQ,GAAG,CAAC;AACZ,QAAQ,GAAG,CAAC;AACZ,QAAQ,GAAG,CACT,mBACA,uCAAuC;AAEzC,QAAQ,GAAG,CACT,wBACA,uCAA4C;AAE9C,QAAQ,GAAG,CAAC,gBAAgB;AAC5B,QAAQ,GAAG,CACT,sCACA,iBAAiB,UAAU,GAAG,MAAM;AAGtC,wBAAwB;AACxB,uCAAkB;;AAElB;AAEA,uCAAsB;;AAEtB;AAGO,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACjE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,UAAU;QACR,QAAQ;YACN,iBAAiB;QACnB;IACF;AACF;AAIO,MAAM,gBACX,gBAAkB,eAAe,yBAC7B,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,wBAAwB;IAChD,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF,KACA;AAGC,MAAM,KAAK;AACX,MAAM,UAAU,SAAS,OAAO;AAGhC,MAAM,oBAAoB;IAC/B,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,UAAU;IACtB,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,yBAAyB,CAAC,CAAC;AACzC;AAGO,MAAM,eAAe;IAC1B,QAAQ,GAAG,CAAC;IACZ;IAEA,oCAAoC;IACpC,uCAAmC;;IAEnC,OAAO;QACL,QAAQ,GAAG,CAAC;IACd;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/hooks/use-auth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist, createJSONStorage } from \"zustand/middleware\";\r\nimport { User } from \"@/lib/types\";\r\nimport { supabase } from \"@/lib/supabase\";\r\nimport {\r\n  AuthError,\r\n  Session,\r\n  User as SupabaseUser,\r\n} from \"@supabase/supabase-js\";\r\nimport { useEffect } from \"react\";\r\n\r\ninterface AuthState {\r\n  user: User | null;\r\n  session: Session | null;\r\n  isAuthenticated: boolean;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n  // Auth methods\r\n  signUp: (\r\n    email: string,\r\n    password: string,\r\n    username: string\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signIn: (\r\n    email: string,\r\n    password: string\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signInWithOAuth: (\r\n    provider: \"google\" | \"azure\" | \"discord\" | \"facebook\"\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signOut: () => Promise<void>;\r\n  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;\r\n  updateProfile: (updates: Partial<User>) => Promise<{ error: Error | null }>;\r\n  uploadAvatar: (\r\n    file: File\r\n  ) => Promise<{ url: string | null; error: Error | null }>;\r\n  // Internal methods\r\n  setSession: (session: Session | null) => void;\r\n  setUser: (user: User | null) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  setError: (error: string | null) => void;\r\n}\r\n\r\nconst useAuthStore = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      user: null,\r\n      session: null,\r\n      isAuthenticated: false,\r\n      isLoading: true,\r\n      error: null,\r\n\r\n      signUp: async (email: string, password: string, username: string) => {\r\n        console.log(\"🚀 Starting signup process...\");\r\n        console.log(\"- Email:\", email);\r\n        console.log(\"- Username:\", username);\r\n        console.log(\"- Supabase client:\", !!supabase);\r\n\r\n        set({ isLoading: true, error: null });\r\n\r\n        try {\r\n          const { data, error } = await supabase.auth.signUp({\r\n            email,\r\n            password,\r\n            options: {\r\n              data: {\r\n                username,\r\n              },\r\n            },\r\n          });\r\n\r\n          console.log(\"📝 Signup response:\", { data, error });\r\n\r\n          if (error) {\r\n            console.error(\"❌ Signup error:\", error);\r\n            set({ error: error.message, isLoading: false });\r\n            return { error };\r\n          }\r\n\r\n          console.log(\"✅ Signup successful!\");\r\n          // User will be created in the database via trigger\r\n          set({ isLoading: false });\r\n          return { error: null };\r\n        } catch (err) {\r\n          console.error(\"💥 Signup exception:\", err);\r\n          const errorMessage =\r\n            err instanceof Error ? err.message : \"Unknown error\";\r\n          set({ error: errorMessage, isLoading: false });\r\n          return { error: { message: errorMessage } as any };\r\n        }\r\n      },\r\n\r\n      signIn: async (email: string, password: string) => {\r\n        set({ isLoading: true, error: null });\r\n\r\n        try {\r\n          const { data, error } = await supabase.auth.signInWithPassword({\r\n            email,\r\n            password,\r\n          });\r\n\r\n          if (error) {\r\n            set({ error: error.message, isLoading: false });\r\n            return { error };\r\n          }\r\n\r\n          // Session will be handled by the auth state change listener\r\n          // But we'll add a timeout to ensure loading doesn't get stuck\r\n          setTimeout(() => {\r\n            const currentState = get();\r\n            if (currentState.isLoading && !currentState.session) {\r\n              console.warn(\"Sign-in timeout, forcing loading to false\");\r\n              set({ isLoading: false });\r\n            }\r\n          }, 5000);\r\n\r\n          return { error: null };\r\n        } catch (err) {\r\n          const errorMessage =\r\n            err instanceof Error ? err.message : \"Sign-in failed\";\r\n          set({ error: errorMessage, isLoading: false });\r\n          return { error: { message: errorMessage } as any };\r\n        }\r\n      },\r\n\r\n      signInWithOAuth: async (\r\n        provider: \"google\" | \"azure\" | \"discord\" | \"facebook\"\r\n      ) => {\r\n        set({ isLoading: true, error: null });\r\n\r\n        const { data, error } = await supabase.auth.signInWithOAuth({\r\n          provider,\r\n          options: {\r\n            redirectTo: `${window.location.origin}/auth/callback`,\r\n          },\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error };\r\n        }\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      signOut: async () => {\r\n        set({ isLoading: true });\r\n        await supabase.auth.signOut();\r\n        set({\r\n          user: null,\r\n          session: null,\r\n          isAuthenticated: false,\r\n          isLoading: false,\r\n          error: null,\r\n        });\r\n      },\r\n\r\n      resetPassword: async (email: string) => {\r\n        const { error } = await supabase.auth.resetPasswordForEmail(email, {\r\n          redirectTo: `${window.location.origin}/auth/reset-password`,\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message });\r\n          return { error };\r\n        }\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      updateProfile: async (updates: Partial<User>) => {\r\n        const { user } = get();\r\n        if (!user) {\r\n          const error = new Error(\"No user logged in\");\r\n          set({ error: error.message });\r\n          return { error };\r\n        }\r\n\r\n        set({ isLoading: true, error: null });\r\n\r\n        // Map camelCase properties to snake_case database columns\r\n        const dbUpdates: any = {};\r\n\r\n        if (updates.avatarUrl !== undefined) {\r\n          dbUpdates.avatar_url = updates.avatarUrl;\r\n        }\r\n        if (updates.bio !== undefined) {\r\n          dbUpdates.bio = updates.bio;\r\n        }\r\n        if (updates.username !== undefined) {\r\n          dbUpdates.username = updates.username;\r\n        }\r\n        if (updates.email !== undefined) {\r\n          dbUpdates.email = updates.email;\r\n        }\r\n        if (updates.contacts?.website !== undefined) {\r\n          dbUpdates.website_url = updates.contacts.website.value;\r\n          dbUpdates.website_public = updates.contacts.website.isPublic;\r\n        }\r\n        if (updates.contacts?.phone !== undefined) {\r\n          dbUpdates.phone = updates.contacts.phone.value;\r\n          dbUpdates.phone_public = updates.contacts.phone.isPublic;\r\n        }\r\n        if (updates.contacts?.messaging !== undefined) {\r\n          dbUpdates.messaging_platform = updates.contacts.messaging.platform;\r\n          dbUpdates.messaging_username = updates.contacts.messaging.username;\r\n          dbUpdates.messaging_public = updates.contacts.messaging.isPublic;\r\n        }\r\n\r\n        const { error } = await supabase\r\n          .from(\"users\")\r\n          .update(dbUpdates)\r\n          .eq(\"id\", user.id);\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error: new Error(error.message) };\r\n        }\r\n\r\n        // Update local user state\r\n        set({\r\n          user: { ...user, ...updates },\r\n          isLoading: false,\r\n        });\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      uploadAvatar: async (file: File) => {\r\n        const { user } = get();\r\n        if (!user) {\r\n          const error = new Error(\"No user logged in\");\r\n          return { url: null, error };\r\n        }\r\n\r\n        const fileExt = file.name.split(\".\").pop();\r\n        const fileName = `${user.id}/avatar.${fileExt}`;\r\n\r\n        const { error: uploadError } = await supabase.storage\r\n          .from(\"avatars\")\r\n          .upload(fileName, file, { upsert: true });\r\n\r\n        if (uploadError) {\r\n          return { url: null, error: new Error(uploadError.message) };\r\n        }\r\n\r\n        const { data } = supabase.storage\r\n          .from(\"avatars\")\r\n          .getPublicUrl(fileName);\r\n\r\n        const avatarUrl = data.publicUrl;\r\n\r\n        // Update user profile with new avatar URL\r\n        const { error: updateError } = await get().updateProfile({ avatarUrl });\r\n\r\n        if (updateError) {\r\n          return { url: null, error: updateError };\r\n        }\r\n\r\n        return { url: avatarUrl, error: null };\r\n      },\r\n\r\n      setSession: (session: Session | null) => {\r\n        set({ session, isAuthenticated: !!session });\r\n      },\r\n\r\n      setUser: (user: User | null) => {\r\n        set({ user, isAuthenticated: !!user, isLoading: false });\r\n      },\r\n\r\n      setLoading: (loading: boolean) => {\r\n        set({ isLoading: loading });\r\n      },\r\n\r\n      setError: (error: string | null) => {\r\n        set({ error });\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n      storage: createJSONStorage(() => localStorage),\r\n      partialize: (state) => ({\r\n        // Only persist user data, not session (Supabase handles session persistence)\r\n        user: state.user,\r\n        // Don't persist loading states to avoid stuck states\r\n      }),\r\n      // Reset loading state on hydration\r\n      onRehydrateStorage: () => (state) => {\r\n        if (state) {\r\n          state.isLoading = true; // Will be set to false by auth initialization\r\n          state.isAuthenticated = !!state.user;\r\n        }\r\n      },\r\n    }\r\n  )\r\n);\r\n\r\n// Helper function to fetch user profile from database\r\nconst fetchUserProfile = async (\r\n  supabaseUser: SupabaseUser\r\n): Promise<User | null> => {\r\n  console.log(\"🔍 Fetching user profile for:\", supabaseUser.id);\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(\"users\")\r\n      .select(\"*\")\r\n      .eq(\"id\", supabaseUser.id)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching user profile:\", {\r\n        message: error.message,\r\n        details: error.details,\r\n        hint: error.hint,\r\n        code: error.code,\r\n        fullError: error,\r\n      });\r\n\r\n      // If user doesn't exist in users table, try to create one\r\n      if (error.code === \"PGRST116\") {\r\n        console.log(\r\n          \"🔄 User not found in users table, attempting to create...\"\r\n        );\r\n        return await createUserProfile(supabaseUser);\r\n      }\r\n\r\n      // For other errors, still try to create the user profile as a fallback\r\n      console.log(\r\n        \"🔄 Database error occurred, attempting to create user profile as fallback...\"\r\n      );\r\n      return await createUserProfile(supabaseUser);\r\n    }\r\n\r\n    console.log(\"✅ User profile fetched successfully:\", data.username);\r\n\r\n    return {\r\n      id: data.id,\r\n      username: data.username,\r\n      email: data.email,\r\n      avatarUrl: data.avatar_url,\r\n      bio: data.bio,\r\n      contacts: {\r\n        website: {\r\n          value: data.website_url || \"\",\r\n          isPublic: data.website_public || false,\r\n        },\r\n        phone: {\r\n          value: data.phone || \"\",\r\n          isPublic: data.phone_public || false,\r\n        },\r\n        messaging: {\r\n          platform: data.messaging_platform || \"\",\r\n          username: data.messaging_username || \"\",\r\n          isPublic: data.messaging_public || false,\r\n        },\r\n      },\r\n    };\r\n  } catch (err) {\r\n    console.error(\"Exception in fetchUserProfile:\", err);\r\n    return null;\r\n  }\r\n};\r\n\r\n// Helper function to create a user profile when one doesn't exist\r\nconst createUserProfile = async (\r\n  supabaseUser: SupabaseUser\r\n): Promise<User | null> => {\r\n  try {\r\n    const username =\r\n      supabaseUser.user_metadata?.username ||\r\n      supabaseUser.email?.split(\"@\")[0] ||\r\n      \"user\";\r\n\r\n    console.log(\"📝 Creating user profile for:\", {\r\n      id: supabaseUser.id,\r\n      email: supabaseUser.email,\r\n      username: username,\r\n    });\r\n\r\n    // Create user profile with all required fields\r\n    const { data, error } = await supabase\r\n      .from(\"users\")\r\n      .insert({\r\n        id: supabaseUser.id,\r\n        username: username,\r\n        email: supabaseUser.email,\r\n        avatar_url: null,\r\n        bio: null,\r\n        website_url: null,\r\n        website_public: false,\r\n        phone: null,\r\n        phone_public: false,\r\n        messaging_platform: null,\r\n        messaging_username: null,\r\n        messaging_public: false,\r\n      })\r\n      .select()\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(\"❌ Failed to create user profile:\", {\r\n        message: error.message,\r\n        details: error.details,\r\n        hint: error.hint,\r\n        code: error.code,\r\n        fullError: error,\r\n      });\r\n\r\n      // Return a minimal user object to prevent app crashes\r\n      return {\r\n        id: supabaseUser.id,\r\n        username: username,\r\n        email: supabaseUser.email || \"\",\r\n        contacts: {\r\n          website: { value: \"\", isPublic: false },\r\n          phone: { value: \"\", isPublic: false },\r\n          messaging: { platform: \"\", username: \"\", isPublic: false },\r\n        },\r\n      };\r\n    }\r\n\r\n    console.log(\"✅ User profile created successfully:\", data);\r\n\r\n    return {\r\n      id: data.id,\r\n      username: data.username,\r\n      email: data.email,\r\n      avatarUrl: data.avatar_url,\r\n      bio: data.bio,\r\n      contacts: {\r\n        website: {\r\n          value: data.website_url || \"\",\r\n          isPublic: data.website_public || false,\r\n        },\r\n        phone: {\r\n          value: data.phone || \"\",\r\n          isPublic: data.phone_public || false,\r\n        },\r\n        messaging: {\r\n          platform: data.messaging_platform || \"\",\r\n          username: data.messaging_username || \"\",\r\n          isPublic: data.messaging_public || false,\r\n        },\r\n      },\r\n    };\r\n  } catch (err) {\r\n    console.error(\"💥 Exception in createUserProfile:\", err);\r\n\r\n    // Return a minimal user object as last resort\r\n    return {\r\n      id: supabaseUser.id,\r\n      username:\r\n        supabaseUser.user_metadata?.username ||\r\n        supabaseUser.email?.split(\"@\")[0] ||\r\n        \"user\",\r\n      email: supabaseUser.email || \"\",\r\n      contacts: {\r\n        website: { value: \"\", isPublic: false },\r\n        phone: { value: \"\", isPublic: false },\r\n        messaging: { platform: \"\", username: \"\", isPublic: false },\r\n      },\r\n    };\r\n  }\r\n};\r\n\r\n// Custom hook to initialize and use the store\r\nexport const useAuth = () => {\r\n  const state = useAuthStore();\r\n\r\n  useEffect(() => {\r\n    let isInitialized = false;\r\n    let timeoutId: NodeJS.Timeout;\r\n\r\n    // Add timeout to prevent infinite loading (only if not already authenticated)\r\n    if (!useAuthStore.getState().isAuthenticated) {\r\n      timeoutId = setTimeout(() => {\r\n        console.warn(\r\n          \"⏰ Auth initialization timeout, forcing loading to false\"\r\n        );\r\n        useAuthStore.getState().setLoading(false);\r\n      }, 10000); // Increased to 10 seconds and only runs if not authenticated\r\n    }\r\n\r\n    // Listen for auth changes (handles sign in/out events after initialization)\r\n    const {\r\n      data: { subscription },\r\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\r\n      console.log(\"Auth state changed:\", event, session);\r\n\r\n      // Skip INITIAL_SESSION event to avoid duplicate processing\r\n      if (event === \"INITIAL_SESSION\") {\r\n        console.log(\r\n          \"⏭️ Skipping INITIAL_SESSION event (handled by initializeAuth)\"\r\n        );\r\n        return;\r\n      }\r\n\r\n      if (timeoutId) clearTimeout(timeoutId); // Clear timeout when auth state changes\r\n\r\n      if (session?.user) {\r\n        console.log(\"👤 Session found, fetching user profile...\");\r\n        try {\r\n          const userProfile = await fetchUserProfile(session.user);\r\n          useAuthStore.getState().setSession(session);\r\n          useAuthStore.getState().setUser(userProfile);\r\n          useAuthStore.getState().setLoading(false);\r\n        } catch (profileError) {\r\n          console.error(\"💥 Failed to fetch user profile:\", profileError);\r\n          // Still set the session but with a minimal user object\r\n          useAuthStore.getState().setSession(session);\r\n          useAuthStore.getState().setUser({\r\n            id: session.user.id,\r\n            username: session.user.email?.split(\"@\")[0] || \"user\",\r\n            email: session.user.email || \"\",\r\n            contacts: {\r\n              website: { value: \"\", isPublic: false },\r\n              phone: { value: \"\", isPublic: false },\r\n              messaging: { platform: \"\", username: \"\", isPublic: false },\r\n            },\r\n          });\r\n          useAuthStore.getState().setLoading(false);\r\n        }\r\n      } else {\r\n        console.log(\"❌ No session found\");\r\n        useAuthStore.getState().setSession(null);\r\n        useAuthStore.getState().setUser(null);\r\n        useAuthStore.getState().setLoading(false);\r\n      }\r\n    });\r\n\r\n    // Initialize auth state\r\n    const initializeAuth = async () => {\r\n      console.log(\"🚀 Initializing auth...\");\r\n      try {\r\n        const {\r\n          data: { session },\r\n        } = await supabase.auth.getSession();\r\n\r\n        if (session?.user) {\r\n          console.log(\"👤 Initial session found, fetching user profile...\");\r\n          try {\r\n            const userProfile = await fetchUserProfile(session.user);\r\n            useAuthStore.getState().setSession(session);\r\n            useAuthStore.getState().setUser(userProfile);\r\n            useAuthStore.getState().setLoading(false);\r\n          } catch (profileError) {\r\n            console.error(\r\n              \"💥 Failed to fetch initial user profile:\",\r\n              profileError\r\n            );\r\n            // Still set the session but with a minimal user object\r\n            useAuthStore.getState().setSession(session);\r\n            useAuthStore.getState().setUser({\r\n              id: session.user.id,\r\n              username: session.user.email?.split(\"@\")[0] || \"user\",\r\n              email: session.user.email || \"\",\r\n              contacts: {\r\n                website: { value: \"\", isPublic: false },\r\n                phone: { value: \"\", isPublic: false },\r\n                messaging: { platform: \"\", username: \"\", isPublic: false },\r\n              },\r\n            });\r\n            useAuthStore.getState().setLoading(false);\r\n          }\r\n        } else {\r\n          console.log(\"❌ No initial session found\");\r\n          useAuthStore.getState().setSession(null);\r\n          useAuthStore.getState().setUser(null);\r\n          useAuthStore.getState().setLoading(false);\r\n        }\r\n\r\n        isInitialized = true;\r\n        console.log(\"✅ Auth initialization complete\");\r\n        if (timeoutId) clearTimeout(timeoutId);\r\n      } catch (error) {\r\n        console.error(\"💥 Error initializing auth:\", error);\r\n        useAuthStore.getState().setLoading(false);\r\n        isInitialized = true;\r\n        if (timeoutId) clearTimeout(timeoutId);\r\n      }\r\n    };\r\n\r\n    initializeAuth();\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n      subscription.unsubscribe();\r\n    };\r\n  }, []);\r\n\r\n  return state;\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AAMA;AAXA;;;;;AA6CA,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,IACxB,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,QAAQ,OAAO,OAAe,UAAkB;YAC9C,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,YAAY;YACxB,QAAQ,GAAG,CAAC,eAAe;YAC3B,QAAQ,GAAG,CAAC,sBAAsB,CAAC,CAAC,sHAAA,CAAA,WAAQ;YAE5C,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;oBACjD;oBACA;oBACA,SAAS;wBACP,MAAM;4BACJ;wBACF;oBACF;gBACF;gBAEA,QAAQ,GAAG,CAAC,uBAAuB;oBAAE;oBAAM;gBAAM;gBAEjD,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,mBAAmB;oBACjC,IAAI;wBAAE,OAAO,MAAM,OAAO;wBAAE,WAAW;oBAAM;oBAC7C,OAAO;wBAAE;oBAAM;gBACjB;gBAEA,QAAQ,GAAG,CAAC;gBACZ,mDAAmD;gBACnD,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;gBACvC,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,OAAO;oBAAE,OAAO;wBAAE,SAAS;oBAAa;gBAAS;YACnD;QACF;QAEA,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;oBAC7D;oBACA;gBACF;gBAEA,IAAI,OAAO;oBACT,IAAI;wBAAE,OAAO,MAAM,OAAO;wBAAE,WAAW;oBAAM;oBAC7C,OAAO;wBAAE;oBAAM;gBACjB;gBAEA,4DAA4D;gBAC5D,8DAA8D;gBAC9D,WAAW;oBACT,MAAM,eAAe;oBACrB,IAAI,aAAa,SAAS,IAAI,CAAC,aAAa,OAAO,EAAE;wBACnD,QAAQ,IAAI,CAAC;wBACb,IAAI;4BAAE,WAAW;wBAAM;oBACzB;gBACF,GAAG;gBAEH,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,KAAK;gBACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;gBACvC,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,OAAO;oBAAE,OAAO;wBAAE,SAAS;oBAAa;gBAAS;YACnD;QACF;QAEA,iBAAiB,OACf;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBAC1D;gBACA,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBACvD;YACF;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,SAAS;YACP,IAAI;gBAAE,WAAW;YAAK;YACtB,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC3B,IAAI;gBACF,MAAM;gBACN,SAAS;gBACT,iBAAiB;gBACjB,WAAW;gBACX,OAAO;YACT;QACF;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC;gBAC3B,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;gBACT,MAAM,QAAQ,IAAI,MAAM;gBACxB,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC;gBAC3B,OAAO;oBAAE;gBAAM;YACjB;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,0DAA0D;YAC1D,MAAM,YAAiB,CAAC;YAExB,IAAI,QAAQ,SAAS,KAAK,WAAW;gBACnC,UAAU,UAAU,GAAG,QAAQ,SAAS;YAC1C;YACA,IAAI,QAAQ,GAAG,KAAK,WAAW;gBAC7B,UAAU,GAAG,GAAG,QAAQ,GAAG;YAC7B;YACA,IAAI,QAAQ,QAAQ,KAAK,WAAW;gBAClC,UAAU,QAAQ,GAAG,QAAQ,QAAQ;YACvC;YACA,IAAI,QAAQ,KAAK,KAAK,WAAW;gBAC/B,UAAU,KAAK,GAAG,QAAQ,KAAK;YACjC;YACA,IAAI,QAAQ,QAAQ,EAAE,YAAY,WAAW;gBAC3C,UAAU,WAAW,GAAG,QAAQ,QAAQ,CAAC,OAAO,CAAC,KAAK;gBACtD,UAAU,cAAc,GAAG,QAAQ,QAAQ,CAAC,OAAO,CAAC,QAAQ;YAC9D;YACA,IAAI,QAAQ,QAAQ,EAAE,UAAU,WAAW;gBACzC,UAAU,KAAK,GAAG,QAAQ,QAAQ,CAAC,KAAK,CAAC,KAAK;gBAC9C,UAAU,YAAY,GAAG,QAAQ,QAAQ,CAAC,KAAK,CAAC,QAAQ;YAC1D;YACA,IAAI,QAAQ,QAAQ,EAAE,cAAc,WAAW;gBAC7C,UAAU,kBAAkB,GAAG,QAAQ,QAAQ,CAAC,SAAS,CAAC,QAAQ;gBAClE,UAAU,kBAAkB,GAAG,QAAQ,QAAQ,CAAC,SAAS,CAAC,QAAQ;gBAClE,UAAU,gBAAgB,GAAG,QAAQ,QAAQ,CAAC,SAAS,CAAC,QAAQ;YAClE;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE,OAAO,IAAI,MAAM,MAAM,OAAO;gBAAE;YAC3C;YAEA,0BAA0B;YAC1B,IAAI;gBACF,MAAM;oBAAE,GAAG,IAAI;oBAAE,GAAG,OAAO;gBAAC;gBAC5B,WAAW;YACb;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,cAAc,OAAO;YACnB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;gBACT,MAAM,QAAQ,IAAI,MAAM;gBACxB,OAAO;oBAAE,KAAK;oBAAM;gBAAM;YAC5B;YAEA,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACxC,MAAM,WAAW,GAAG,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS;YAE/C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,WACL,MAAM,CAAC,UAAU,MAAM;gBAAE,QAAQ;YAAK;YAEzC,IAAI,aAAa;gBACf,OAAO;oBAAE,KAAK;oBAAM,OAAO,IAAI,MAAM,YAAY,OAAO;gBAAE;YAC5D;YAEA,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,WACL,YAAY,CAAC;YAEhB,MAAM,YAAY,KAAK,SAAS;YAEhC,0CAA0C;YAC1C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,MAAM,aAAa,CAAC;gBAAE;YAAU;YAErE,IAAI,aAAa;gBACf,OAAO;oBAAE,KAAK;oBAAM,OAAO;gBAAY;YACzC;YAEA,OAAO;gBAAE,KAAK;gBAAW,OAAO;YAAK;QACvC;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;gBAAS,iBAAiB,CAAC,CAAC;YAAQ;QAC5C;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;gBAAM,WAAW;YAAM;QACxD;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YACtB,6EAA6E;YAC7E,MAAM,MAAM,IAAI;QAElB,CAAC;IACD,mCAAmC;IACnC,oBAAoB,IAAM,CAAC;YACzB,IAAI,OAAO;gBACT,MAAM,SAAS,GAAG,MAAM,8CAA8C;gBACtE,MAAM,eAAe,GAAG,CAAC,CAAC,MAAM,IAAI;YACtC;QACF;AACF;AAIJ,sDAAsD;AACtD,MAAM,mBAAmB,OACvB;IAEA,QAAQ,GAAG,CAAC,iCAAiC,aAAa,EAAE;IAC5D,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;gBAC5C,SAAS,MAAM,OAAO;gBACtB,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,MAAM,MAAM,IAAI;gBAChB,WAAW;YACb;YAEA,0DAA0D;YAC1D,IAAI,MAAM,IAAI,KAAK,YAAY;gBAC7B,QAAQ,GAAG,CACT;gBAEF,OAAO,MAAM,kBAAkB;YACjC;YAEA,uEAAuE;YACvE,QAAQ,GAAG,CACT;YAEF,OAAO,MAAM,kBAAkB;QACjC;QAEA,QAAQ,GAAG,CAAC,wCAAwC,KAAK,QAAQ;QAEjE,OAAO;YACL,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,UAAU;YAC1B,KAAK,KAAK,GAAG;YACb,UAAU;gBACR,SAAS;oBACP,OAAO,KAAK,WAAW,IAAI;oBAC3B,UAAU,KAAK,cAAc,IAAI;gBACnC;gBACA,OAAO;oBACL,OAAO,KAAK,KAAK,IAAI;oBACrB,UAAU,KAAK,YAAY,IAAI;gBACjC;gBACA,WAAW;oBACT,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,gBAAgB,IAAI;gBACrC;YACF;QACF;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAEA,kEAAkE;AAClE,MAAM,oBAAoB,OACxB;IAEA,IAAI;QACF,MAAM,WACJ,aAAa,aAAa,EAAE,YAC5B,aAAa,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IACjC;QAEF,QAAQ,GAAG,CAAC,iCAAiC;YAC3C,IAAI,aAAa,EAAE;YACnB,OAAO,aAAa,KAAK;YACzB,UAAU;QACZ;QAEA,+CAA+C;QAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;YACN,IAAI,aAAa,EAAE;YACnB,UAAU;YACV,OAAO,aAAa,KAAK;YACzB,YAAY;YACZ,KAAK;YACL,aAAa;YACb,gBAAgB;YAChB,OAAO;YACP,cAAc;YACd,oBAAoB;YACpB,oBAAoB;YACpB,kBAAkB;QACpB,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;gBAChD,SAAS,MAAM,OAAO;gBACtB,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,MAAM,MAAM,IAAI;gBAChB,WAAW;YACb;YAEA,sDAAsD;YACtD,OAAO;gBACL,IAAI,aAAa,EAAE;gBACnB,UAAU;gBACV,OAAO,aAAa,KAAK,IAAI;gBAC7B,UAAU;oBACR,SAAS;wBAAE,OAAO;wBAAI,UAAU;oBAAM;oBACtC,OAAO;wBAAE,OAAO;wBAAI,UAAU;oBAAM;oBACpC,WAAW;wBAAE,UAAU;wBAAI,UAAU;wBAAI,UAAU;oBAAM;gBAC3D;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,wCAAwC;QAEpD,OAAO;YACL,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,UAAU;YAC1B,KAAK,KAAK,GAAG;YACb,UAAU;gBACR,SAAS;oBACP,OAAO,KAAK,WAAW,IAAI;oBAC3B,UAAU,KAAK,cAAc,IAAI;gBACnC;gBACA,OAAO;oBACL,OAAO,KAAK,KAAK,IAAI;oBACrB,UAAU,KAAK,YAAY,IAAI;gBACjC;gBACA,WAAW;oBACT,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,gBAAgB,IAAI;gBACrC;YACF;QACF;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,sCAAsC;QAEpD,8CAA8C;QAC9C,OAAO;YACL,IAAI,aAAa,EAAE;YACnB,UACE,aAAa,aAAa,EAAE,YAC5B,aAAa,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IACjC;YACF,OAAO,aAAa,KAAK,IAAI;YAC7B,UAAU;gBACR,SAAS;oBAAE,OAAO;oBAAI,UAAU;gBAAM;gBACtC,OAAO;oBAAE,OAAO;oBAAI,UAAU;gBAAM;gBACpC,WAAW;oBAAE,UAAU;oBAAI,UAAU;oBAAI,UAAU;gBAAM;YAC3D;QACF;IACF;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,QAAQ;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;QACpB,IAAI;QAEJ,8EAA8E;QAC9E,IAAI,CAAC,aAAa,QAAQ,GAAG,eAAe,EAAE;YAC5C,YAAY,WAAW;gBACrB,QAAQ,IAAI,CACV;gBAEF,aAAa,QAAQ,GAAG,UAAU,CAAC;YACrC,GAAG,QAAQ,6DAA6D;QAC1E;QAEA,4EAA4E;QAC5E,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;YAChD,QAAQ,GAAG,CAAC,uBAAuB,OAAO;YAE1C,2DAA2D;YAC3D,IAAI,UAAU,mBAAmB;gBAC/B,QAAQ,GAAG,CACT;gBAEF;YACF;YAEA,IAAI,WAAW,aAAa,YAAY,wCAAwC;YAEhF,IAAI,SAAS,MAAM;gBACjB,QAAQ,GAAG,CAAC;gBACZ,IAAI;oBACF,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;oBACvD,aAAa,QAAQ,GAAG,UAAU,CAAC;oBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;oBAChC,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACrC,EAAE,OAAO,cAAc;oBACrB,QAAQ,KAAK,CAAC,oCAAoC;oBAClD,uDAAuD;oBACvD,aAAa,QAAQ,GAAG,UAAU,CAAC;oBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;wBAC9B,IAAI,QAAQ,IAAI,CAAC,EAAE;wBACnB,UAAU,QAAQ,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;wBAC/C,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;wBAC7B,UAAU;4BACR,SAAS;gCAAE,OAAO;gCAAI,UAAU;4BAAM;4BACtC,OAAO;gCAAE,OAAO;gCAAI,UAAU;4BAAM;4BACpC,WAAW;gCAAE,UAAU;gCAAI,UAAU;gCAAI,UAAU;4BAAM;wBAC3D;oBACF;oBACA,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACrC;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;gBAChC,aAAa,QAAQ,GAAG,UAAU,CAAC;YACrC;QACF;QAEA,wBAAwB;QACxB,MAAM,iBAAiB;YACrB,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;gBAElC,IAAI,SAAS,MAAM;oBACjB,QAAQ,GAAG,CAAC;oBACZ,IAAI;wBACF,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;wBACvD,aAAa,QAAQ,GAAG,UAAU,CAAC;wBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;wBAChC,aAAa,QAAQ,GAAG,UAAU,CAAC;oBACrC,EAAE,OAAO,cAAc;wBACrB,QAAQ,KAAK,CACX,4CACA;wBAEF,uDAAuD;wBACvD,aAAa,QAAQ,GAAG,UAAU,CAAC;wBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;4BAC9B,IAAI,QAAQ,IAAI,CAAC,EAAE;4BACnB,UAAU,QAAQ,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;4BAC/C,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;4BAC7B,UAAU;gCACR,SAAS;oCAAE,OAAO;oCAAI,UAAU;gCAAM;gCACtC,OAAO;oCAAE,OAAO;oCAAI,UAAU;gCAAM;gCACpC,WAAW;oCAAE,UAAU;oCAAI,UAAU;oCAAI,UAAU;gCAAM;4BAC3D;wBACF;wBACA,aAAa,QAAQ,GAAG,UAAU,CAAC;oBACrC;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,aAAa,QAAQ,GAAG,UAAU,CAAC;oBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;oBAChC,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACrC;gBAEA,gBAAgB;gBAChB,QAAQ,GAAG,CAAC;gBACZ,IAAI,WAAW,aAAa;YAC9B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,gBAAgB;gBAChB,IAAI,WAAW,aAAa;YAC9B;QACF;QAEA;QAEA,OAAO;YACL,IAAI,WAAW,aAAa;YAC5B,aAAa,WAAW;QAC1B;IACF,GAAG,EAAE;IAEL,OAAO;AACT", "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/auth/protected-route.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/hooks/use-auth';\nimport { Skeleton } from '@/components/ui/skeleton';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  redirectTo?: string;\n  requireAuth?: boolean;\n}\n\nexport default function ProtectedRoute({ \n  children, \n  redirectTo = '/login',\n  requireAuth = true \n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && requireAuth && !isAuthenticated) {\n      router.push(redirectTo);\n    }\n  }, [isAuthenticated, isLoading, requireAuth, redirectTo, router]);\n\n  // Show loading state while checking authentication\n  if (isLoading) {\n    return (\n      <div className=\"flex flex-col min-h-screen bg-background\">\n        <header className=\"flex items-center justify-between p-4 border-b\">\n          <Skeleton className=\"h-8 w-32\" />\n          <div className=\"flex items-center gap-4\">\n            <Skeleton className=\"h-8 w-8 rounded-full\" />\n            <Skeleton className=\"h-8 w-8 rounded-full\" />\n            <Skeleton className=\"h-8 w-8 rounded-full\" />\n          </div>\n        </header>\n        <main className=\"flex-1 p-4 md:p-8\">\n          <div className=\"max-w-4xl mx-auto space-y-4\">\n            <Skeleton className=\"h-8 w-48\" />\n            <Skeleton className=\"h-4 w-full\" />\n            <Skeleton className=\"h-4 w-3/4\" />\n            <Skeleton className=\"h-4 w-1/2\" />\n          </div>\n        </main>\n      </div>\n    );\n  }\n\n  // If authentication is required but user is not authenticated, don't render children\n  if (requireAuth && !isAuthenticated) {\n    return null;\n  }\n\n  // If authentication is not required or user is authenticated, render children\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAae,SAAS,eAAe,EACrC,QAAQ,EACR,aAAa,QAAQ,EACrB,cAAc,IAAI,EACE;IACpB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,eAAe,CAAC,iBAAiB;YACjD,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;QAAa;QAAY;KAAO;IAEhE,mDAAmD;IACnD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAGxB,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAK9B;IAEA,qFAAqF;IACrF,IAAI,eAAe,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,8EAA8E;IAC9E,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAvatar.displayName = AvatarPrimitive.Root.displayName\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1023, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport {cn} from '@/lib/utils';\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\r\n  ({className, ...props}, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = 'Textarea';\r\n\r\nexport {Textarea};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1506, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/lib/supabase-posts.ts"], "sourcesContent": ["import { supabase, storage } from \"./supabase\";\nimport type { Post } from \"./types\";\n\n// Upload image to Supabase Storage\nexport const uploadImage = async (\n  file: File,\n  path: string\n): Promise<string> => {\n  try {\n    console.log(\"Uploading image to Supabase Storage...\");\n\n    const fileName = `${Date.now()}_${file.name}`;\n    const filePath = `${path}/${fileName}`;\n\n    const { data, error } = await storage\n      .from(\"post-images\")\n      .upload(filePath, file, {\n        cacheControl: \"3600\",\n        upsert: false,\n      });\n\n    if (error) {\n      throw error;\n    }\n\n    // Get public URL\n    const {\n      data: { publicUrl },\n    } = storage.from(\"post-images\").getPublicUrl(data.path);\n\n    console.log(\"✅ Image uploaded successfully:\", publicUrl);\n    return publicUrl;\n  } catch (error) {\n    console.error(\"❌ Error uploading image to Supabase Storage:\", error);\n    throw new Error(`Failed to upload image: ${error}`);\n  }\n};\n\n// Upload base64 image to Supabase Storage\nexport const uploadBase64Image = async (\n  base64Data: string,\n  path: string\n): Promise<string> => {\n  try {\n    console.log(\"Converting and uploading base64 image to Supabase Storage...\");\n    console.log(\"- Path:\", path);\n    console.log(\"- Base64 data length:\", base64Data.length);\n\n    // Convert base64 to blob\n    const response = await fetch(base64Data);\n    const blob = await response.blob();\n    console.log(\"- Blob size:\", blob.size, \"bytes\");\n    console.log(\"- Blob type:\", blob.type);\n\n    const fileName = `${Date.now()}.png`;\n    const filePath = `${path}/${fileName}`;\n    console.log(\"- Upload path:\", filePath);\n\n    const { data, error } = await storage\n      .from(\"post-images\")\n      .upload(filePath, blob, {\n        cacheControl: \"3600\",\n        upsert: false,\n      });\n\n    if (error) {\n      console.error(\"❌ Storage upload error:\", {\n        message: error.message,\n        error: error,\n      });\n      throw error;\n    }\n\n    console.log(\"✅ Storage upload successful:\", data);\n\n    // Get public URL\n    const {\n      data: { publicUrl },\n    } = storage.from(\"post-images\").getPublicUrl(data.path);\n\n    console.log(\"✅ Base64 image uploaded successfully:\", publicUrl);\n    return publicUrl;\n  } catch (error) {\n    console.error(\n      \"❌ Error uploading base64 image to Supabase Storage:\",\n      error\n    );\n    throw new Error(`Failed to upload base64 image: ${error}`);\n  }\n};\n\n// Add a new post\nexport const addPost = async (post: Omit<Post, \"id\">): Promise<string> => {\n  try {\n    console.log(\"🚀 Starting post creation process...\");\n    console.log(\"- Author:\", post.author.username);\n    console.log(\"- Caption:\", post.caption);\n    console.log(\n      \"- Image type:\",\n      post.imageUrl.startsWith(\"data:\") ? \"base64\" : \"url\"\n    );\n\n    // If imageUrl is base64, upload it to storage\n    let imageUrl = post.imageUrl;\n    if (post.imageUrl.startsWith(\"data:\")) {\n      console.log(\"📤 Uploading base64 image to storage...\");\n      imageUrl = await uploadBase64Image(\n        post.imageUrl,\n        `posts/${post.author.username}`\n      );\n      console.log(\"✅ Image uploaded, URL:\", imageUrl);\n    }\n\n    const postData = {\n      author_username: post.author.username,\n      author_avatar_url: post.author.avatarUrl,\n      image_url: imageUrl,\n      caption: post.caption,\n      likes: post.likes || 0,\n      comments: post.comments || 0,\n      created_at: new Date().toISOString(),\n    };\n\n    console.log(\"💾 Inserting post data into database...\");\n    console.log(\"- Post data:\", postData);\n\n    const { data, error } = await supabase\n      .from(\"posts\")\n      .insert([postData])\n      .select()\n      .single();\n\n    if (error) {\n      console.error(\"❌ Database insert error:\", {\n        message: error.message,\n        details: error.details,\n        hint: error.hint,\n        code: error.code,\n        error: error,\n      });\n      throw error;\n    }\n\n    console.log(\"✅ Post created successfully with ID:\", data.id);\n    return data.id;\n  } catch (error) {\n    console.error(\"💥 Error adding post:\", error);\n    throw error;\n  }\n};\n\n// Get all posts\nexport const getAllPosts = async (): Promise<Post[]> => {\n  try {\n    const { data, error } = await supabase\n      .from(\"posts\")\n      .select(\"*\")\n      .order(\"created_at\", { ascending: false });\n\n    if (error) {\n      throw error;\n    }\n\n    return data.map(convertSupabaseToPost);\n  } catch (error) {\n    console.error(\"Error fetching posts:\", error);\n    throw error;\n  }\n};\n\n// Get posts by user\nexport const getPostsByUser = async (username: string): Promise<Post[]> => {\n  try {\n    const { data, error } = await supabase\n      .from(\"posts\")\n      .select(\"*\")\n      .eq(\"author_username\", username)\n      .order(\"created_at\", { ascending: false });\n\n    if (error) {\n      throw error;\n    }\n\n    return data.map(convertSupabaseToPost);\n  } catch (error) {\n    console.error(\"Error fetching user posts:\", error);\n    throw error;\n  }\n};\n\n// Update a post\nexport const updatePost = async (updatedPost: Post): Promise<void> => {\n  try {\n    const { error } = await supabase\n      .from(\"posts\")\n      .update({\n        caption: updatedPost.caption,\n        likes: updatedPost.likes,\n        comments: updatedPost.comments,\n        updated_at: new Date().toISOString(),\n      })\n      .eq(\"id\", updatedPost.id);\n\n    if (error) {\n      throw error;\n    }\n  } catch (error) {\n    console.error(\"Error updating post:\", error);\n    throw error;\n  }\n};\n\n// Delete a post\nexport const deletePost = async (postId: string): Promise<void> => {\n  try {\n    const { error } = await supabase.from(\"posts\").delete().eq(\"id\", postId);\n\n    if (error) {\n      throw error;\n    }\n  } catch (error) {\n    console.error(\"Error deleting post:\", error);\n    throw error;\n  }\n};\n\n// Subscribe to all posts with real-time updates\nexport const subscribeToAllPosts = (callback: (posts: Post[]) => void) => {\n  console.log(\"Setting up real-time subscription for all posts...\");\n\n  // Initial fetch\n  getAllPosts().then(callback).catch(console.error);\n\n  // Set up real-time subscription\n  const subscription = supabase\n    .channel(\"posts\")\n    .on(\n      \"postgres_changes\",\n      { event: \"*\", schema: \"public\", table: \"posts\" },\n      () => {\n        // Refetch all posts when any change occurs\n        getAllPosts().then(callback).catch(console.error);\n      }\n    )\n    .subscribe();\n\n  // Return unsubscribe function\n  return () => {\n    console.log(\"Unsubscribing from posts real-time updates\");\n    subscription.unsubscribe();\n  };\n};\n\n// Subscribe to user posts with real-time updates\nexport const subscribeToUserPosts = (\n  username: string,\n  callback: (posts: Post[]) => void\n) => {\n  console.log(`Setting up real-time subscription for ${username} posts...`);\n\n  // Initial fetch\n  getPostsByUser(username).then(callback).catch(console.error);\n\n  // Set up real-time subscription\n  const subscription = supabase\n    .channel(`user-posts-${username}`)\n    .on(\n      \"postgres_changes\",\n      {\n        event: \"*\",\n        schema: \"public\",\n        table: \"posts\",\n        filter: `author_username=eq.${username}`,\n      },\n      () => {\n        // Refetch user posts when any change occurs\n        getPostsByUser(username).then(callback).catch(console.error);\n      }\n    )\n    .subscribe();\n\n  // Return unsubscribe function\n  return () => {\n    console.log(`Unsubscribing from ${username} posts real-time updates`);\n    subscription.unsubscribe();\n  };\n};\n\n// Convert Supabase row to Post type\nconst convertSupabaseToPost = (row: any): Post => ({\n  id: row.id,\n  author: {\n    username: row.author_username,\n    avatarUrl: row.author_avatar_url,\n  },\n  imageUrl: row.image_url,\n  caption: row.caption,\n  likes: row.likes,\n  comments: row.comments,\n  createdAt: row.created_at,\n});\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAIO,MAAM,cAAc,OACzB,MACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,WAAW,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE;QAC7C,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,UAAU;QAEtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,UAAO,CAClC,IAAI,CAAC,eACL,MAAM,CAAC,UAAU,MAAM;YACtB,cAAc;YACd,QAAQ;QACV;QAEF,IAAI,OAAO;YACT,MAAM;QACR;QAEA,iBAAiB;QACjB,MAAM,EACJ,MAAM,EAAE,SAAS,EAAE,EACpB,GAAG,sHAAA,CAAA,UAAO,CAAC,IAAI,CAAC,eAAe,YAAY,CAAC,KAAK,IAAI;QAEtD,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,OAAO;IACpD;AACF;AAGO,MAAM,oBAAoB,OAC/B,YACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,WAAW;QACvB,QAAQ,GAAG,CAAC,yBAAyB,WAAW,MAAM;QAEtD,yBAAyB;QACzB,MAAM,WAAW,MAAM,MAAM;QAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,gBAAgB,KAAK,IAAI,EAAE;QACvC,QAAQ,GAAG,CAAC,gBAAgB,KAAK,IAAI;QAErC,MAAM,WAAW,GAAG,KAAK,GAAG,GAAG,IAAI,CAAC;QACpC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,UAAU;QACtC,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,UAAO,CAClC,IAAI,CAAC,eACL,MAAM,CAAC,UAAU,MAAM;YACtB,cAAc;YACd,QAAQ;QACV;QAEF,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;gBACvC,SAAS,MAAM,OAAO;gBACtB,OAAO;YACT;YACA,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,gCAAgC;QAE5C,iBAAiB;QACjB,MAAM,EACJ,MAAM,EAAE,SAAS,EAAE,EACpB,GAAG,sHAAA,CAAA,UAAO,CAAC,IAAI,CAAC,eAAe,YAAY,CAAC,KAAK,IAAI;QAEtD,QAAQ,GAAG,CAAC,yCAAyC;QACrD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,uDACA;QAEF,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,OAAO;IAC3D;AACF;AAGO,MAAM,UAAU,OAAO;IAC5B,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,aAAa,KAAK,MAAM,CAAC,QAAQ;QAC7C,QAAQ,GAAG,CAAC,cAAc,KAAK,OAAO;QACtC,QAAQ,GAAG,CACT,iBACA,KAAK,QAAQ,CAAC,UAAU,CAAC,WAAW,WAAW;QAGjD,8CAA8C;QAC9C,IAAI,WAAW,KAAK,QAAQ;QAC5B,IAAI,KAAK,QAAQ,CAAC,UAAU,CAAC,UAAU;YACrC,QAAQ,GAAG,CAAC;YACZ,WAAW,MAAM,kBACf,KAAK,QAAQ,EACb,CAAC,MAAM,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE;YAEjC,QAAQ,GAAG,CAAC,0BAA0B;QACxC;QAEA,MAAM,WAAW;YACf,iBAAiB,KAAK,MAAM,CAAC,QAAQ;YACrC,mBAAmB,KAAK,MAAM,CAAC,SAAS;YACxC,WAAW;YACX,SAAS,KAAK,OAAO;YACrB,OAAO,KAAK,KAAK,IAAI;YACrB,UAAU,KAAK,QAAQ,IAAI;YAC3B,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,gBAAgB;QAE5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;YAAC;SAAS,EACjB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;gBACxC,SAAS,MAAM,OAAO;gBACtB,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,MAAM,MAAM,IAAI;gBAChB,OAAO;YACT;YACA,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,wCAAwC,KAAK,EAAE;QAC3D,OAAO,KAAK,EAAE;IAChB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,MAAM;QACR;QAEA,OAAO,KAAK,GAAG,CAAC;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,mBAAmB,UACtB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,MAAM;QACR;QAEA,OAAO,KAAK,GAAG,CAAC;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;YACN,SAAS,YAAY,OAAO;YAC5B,OAAO,YAAY,KAAK;YACxB,UAAU,YAAY,QAAQ;YAC9B,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,YAAY,EAAE;QAE1B,IAAI,OAAO;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,MAAM;QAEjE,IAAI,OAAO;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,sBAAsB,CAAC;IAClC,QAAQ,GAAG,CAAC;IAEZ,gBAAgB;IAChB,cAAc,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,KAAK;IAEhD,gCAAgC;IAChC,MAAM,eAAe,sHAAA,CAAA,WAAQ,CAC1B,OAAO,CAAC,SACR,EAAE,CACD,oBACA;QAAE,OAAO;QAAK,QAAQ;QAAU,OAAO;IAAQ,GAC/C;QACE,2CAA2C;QAC3C,cAAc,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,KAAK;IAClD,GAED,SAAS;IAEZ,8BAA8B;IAC9B,OAAO;QACL,QAAQ,GAAG,CAAC;QACZ,aAAa,WAAW;IAC1B;AACF;AAGO,MAAM,uBAAuB,CAClC,UACA;IAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,SAAS,SAAS,CAAC;IAExE,gBAAgB;IAChB,eAAe,UAAU,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,KAAK;IAE3D,gCAAgC;IAChC,MAAM,eAAe,sHAAA,CAAA,WAAQ,CAC1B,OAAO,CAAC,CAAC,WAAW,EAAE,UAAU,EAChC,EAAE,CACD,oBACA;QACE,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ,CAAC,mBAAmB,EAAE,UAAU;IAC1C,GACA;QACE,4CAA4C;QAC5C,eAAe,UAAU,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,KAAK;IAC7D,GAED,SAAS;IAEZ,8BAA8B;IAC9B,OAAO;QACL,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,wBAAwB,CAAC;QACpE,aAAa,WAAW;IAC1B;AACF;AAEA,oCAAoC;AACpC,MAAM,wBAAwB,CAAC,MAAmB,CAAC;QACjD,IAAI,IAAI,EAAE;QACV,QAAQ;YACN,UAAU,IAAI,eAAe;YAC7B,WAAW,IAAI,iBAAiB;QAClC;QACA,UAAU,IAAI,SAAS;QACvB,SAAS,IAAI,OAAO;QACpB,OAAO,IAAI,KAAK;QAChB,UAAU,IAAI,QAAQ;QACtB,WAAW,IAAI,UAAU;IAC3B,CAAC", "debugId": null}}, {"offset": {"line": 1731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/hooks/use-post-store.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport type { Post } from \"@/lib/types\";\r\nimport {\r\n  addPost as supabaseAddPost,\r\n  updatePost as supabaseUpdatePost,\r\n  subscribeToAllPosts,\r\n  getAllPosts,\r\n} from \"@/lib/supabase-posts\";\r\n\r\ninterface PostState {\r\n  posts: Post[];\r\n  isLoading: boolean;\r\n  error: string | null;\r\n  addPost: (post: Omit<Post, \"id\">) => Promise<void>;\r\n  updatePost: (updatedPost: Post) => Promise<void>;\r\n  setPosts: (posts: Post[]) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  setError: (error: string | null) => void;\r\n  initializePosts: () => Promise<void>;\r\n}\r\n\r\nexport const usePostStore = create<PostState>((set, get) => ({\r\n  posts: [],\r\n  isLoading: false,\r\n  error: null,\r\n\r\n  addPost: async (post) => {\r\n    try {\r\n      set({ isLoading: true, error: null });\r\n      const postId = await supabaseAddPost(post);\r\n      // The real-time listener will update the posts automatically\r\n      console.log(\"Post added successfully with ID:\", postId);\r\n    } catch (error) {\r\n      console.error(\"Error adding post:\", error);\r\n      set({ error: \"Failed to add post\" });\r\n    } finally {\r\n      set({ isLoading: false });\r\n    }\r\n  },\r\n\r\n  updatePost: async (updatedPost) => {\r\n    try {\r\n      set({ isLoading: true, error: null });\r\n      await supabaseUpdatePost(updatedPost);\r\n      // The real-time listener will update the posts automatically\r\n      console.log(\"Post updated successfully\");\r\n    } catch (error) {\r\n      console.error(\"Error updating post:\", error);\r\n      set({ error: \"Failed to update post\" });\r\n    } finally {\r\n      set({ isLoading: false });\r\n    }\r\n  },\r\n\r\n  setPosts: (posts) => set({ posts }),\r\n  setLoading: (loading) => set({ isLoading: loading }),\r\n  setError: (error) => set({ error }),\r\n\r\n  initializePosts: async () => {\r\n    try {\r\n      set({ isLoading: true, error: null });\r\n\r\n      // Set up real-time listener\r\n      const unsubscribe = subscribeToAllPosts((posts) => {\r\n        set({ posts, isLoading: false });\r\n      });\r\n\r\n      // Store unsubscribe function for cleanup\r\n      (get() as any).unsubscribe = unsubscribe;\r\n    } catch (error) {\r\n      console.error(\"Error initializing posts:\", error);\r\n      set({ error: \"Failed to load posts\", isLoading: false });\r\n\r\n      // Fallback to one-time fetch\r\n      try {\r\n        const posts = await getAllPosts();\r\n        set({ posts });\r\n      } catch (fallbackError) {\r\n        console.error(\"Fallback fetch also failed:\", fallbackError);\r\n      }\r\n    }\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;AAEA;AAEA;AAJA;;;AAuBO,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC3D,OAAO,EAAE;QACT,WAAW;QACX,OAAO;QAEP,SAAS,OAAO;YACd,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACnC,MAAM,SAAS,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAe,AAAD,EAAE;gBACrC,6DAA6D;gBAC7D,QAAQ,GAAG,CAAC,oCAAoC;YAClD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,IAAI;oBAAE,OAAO;gBAAqB;YACpC,SAAU;gBACR,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;QAEA,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACnC,MAAM,CAAA,GAAA,+HAAA,CAAA,aAAkB,AAAD,EAAE;gBACzB,6DAA6D;gBAC7D,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI;oBAAE,OAAO;gBAAwB;YACvC,SAAU;gBACR,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;QAEA,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAClD,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,iBAAiB;YACf,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,4BAA4B;gBAC5B,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE,CAAC;oBACvC,IAAI;wBAAE;wBAAO,WAAW;oBAAM;gBAChC;gBAEA,yCAAyC;gBACxC,MAAc,WAAW,GAAG;YAC/B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,IAAI;oBAAE,OAAO;oBAAwB,WAAW;gBAAM;gBAEtD,6BAA6B;gBAC7B,IAAI;oBACF,MAAM,QAAQ,MAAM,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;oBAC9B,IAAI;wBAAE;oBAAM;gBACd,EAAE,OAAO,eAAe;oBACtB,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1831, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/new-post-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect, useCallback } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>alogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogFooter,\r\n  DialogClose,\r\n  DialogDescription,\r\n} from \"@/components/ui/dialog\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Camera, Image as ImageIcon, Upload, X } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"./ui/alert\";\r\nimport type { User, Post } from \"@/lib/types\";\r\nimport { usePostStore } from \"@/hooks/use-post-store\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\n\r\ninterface NewPostDialogProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (isOpen: boolean) => void;\r\n  user: User;\r\n}\r\n\r\nexport default function NewPostDialog({\r\n  isOpen,\r\n  onOpenChange,\r\n  user,\r\n}: NewPostDialogProps) {\r\n  const [image, setImage] = useState<string | null>(null);\r\n  const [caption, setCaption] = useState(\"\");\r\n  const [isCameraActive, setIsCameraActive] = useState(false);\r\n  const [hasCameraPermission, setHasCameraPermission] = useState<\r\n    boolean | null\r\n  >(null);\r\n  const [isDragOver, setIsDragOver] = useState(false);\r\n  const videoRef = useRef<HTMLVideoElement>(null);\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const { toast } = useToast();\r\n  const { addPost } = usePostStore();\r\n\r\n  const cleanupCamera = useCallback(() => {\r\n    if (videoRef.current && videoRef.current.srcObject) {\r\n      const stream = videoRef.current.srcObject as MediaStream;\r\n      stream.getTracks().forEach((track) => track.stop());\r\n      videoRef.current.srcObject = null;\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isCameraActive) {\r\n      const getCameraPermission = async () => {\r\n        try {\r\n          const stream = await navigator.mediaDevices.getUserMedia({\r\n            video: true,\r\n          });\r\n          setHasCameraPermission(true);\r\n          if (videoRef.current) {\r\n            videoRef.current.srcObject = stream;\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error accessing camera:\", error);\r\n          setHasCameraPermission(false);\r\n          toast({\r\n            variant: \"destructive\",\r\n            title: \"Camera Access Denied\",\r\n            description:\r\n              \"Please enable camera permissions in your browser settings.\",\r\n          });\r\n          setIsCameraActive(false);\r\n        }\r\n      };\r\n      getCameraPermission();\r\n    } else {\r\n      cleanupCamera();\r\n    }\r\n\r\n    return () => {\r\n      cleanupCamera();\r\n    };\r\n  }, [isCameraActive, toast, cleanupCamera]);\r\n\r\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (event.target.files && event.target.files[0]) {\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => setImage(e.target?.result as string);\r\n      reader.readAsDataURL(event.target.files[0]);\r\n      setIsCameraActive(false);\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = (file: File) => {\r\n    if (file && file.type.startsWith(\"image/\")) {\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => setImage(e.target?.result as string);\r\n      reader.readAsDataURL(file);\r\n      setIsCameraActive(false);\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n    const files = e.dataTransfer.files;\r\n    if (files.length > 0) {\r\n      handleFileUpload(files[0]);\r\n    }\r\n  };\r\n\r\n  const handleCapture = () => {\r\n    if (videoRef.current && canvasRef.current) {\r\n      const video = videoRef.current;\r\n      const canvas = canvasRef.current;\r\n      canvas.width = video.videoWidth;\r\n      canvas.height = video.videoHeight;\r\n      const context = canvas.getContext(\"2d\");\r\n      context?.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);\r\n      const dataUrl = canvas.toDataURL(\"image/png\");\r\n      setImage(dataUrl);\r\n      setIsCameraActive(false);\r\n    }\r\n  };\r\n\r\n  const resetDialog = () => {\r\n    setImage(null);\r\n    setCaption(\"\");\r\n    setIsCameraActive(false);\r\n    cleanupCamera();\r\n  };\r\n\r\n  const handleClose = (open: boolean) => {\r\n    if (!open) {\r\n      resetDialog();\r\n    }\r\n    onOpenChange(open);\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!image) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"No Image\",\r\n        description: \"Please select an image or take a photo.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const newPost = {\r\n        author: {\r\n          username: user.username,\r\n          avatarUrl: user.avatarUrl || \"\",\r\n        },\r\n        imageUrl: image,\r\n        caption: caption,\r\n        likes: 0,\r\n        comments: 0,\r\n        createdAt: new Date().toISOString(),\r\n      };\r\n\r\n      await addPost(newPost);\r\n\r\n      toast({\r\n        title: \"Post Created\",\r\n        description: \"Your new post has been added to the feed.\",\r\n      });\r\n\r\n      handleClose(false);\r\n    } catch (error) {\r\n      console.error(\"Error creating post:\", error);\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Error\",\r\n        description: \"Failed to create post. Please try again.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={handleClose}>\r\n      <DialogContent className=\"max-w-4xl lg:max-w-5xl w-[95vw]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Create New Post</DialogTitle>\r\n          <DialogDescription>\r\n            Upload a photo or use your camera, write a caption, and share it\r\n            with your followers.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 py-4\">\r\n          <div\r\n            className={`flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-6 min-h-[28rem] lg:min-h-[32rem] w-full transition-all duration-200 ${\r\n              isDragOver\r\n                ? \"border-primary bg-primary/10 scale-[1.02]\"\r\n                : \"border-muted-foreground/25 bg-muted/50 hover:border-muted-foreground/40 hover:bg-muted/70\"\r\n            }`}\r\n            onDragOver={handleDragOver}\r\n            onDragLeave={handleDragLeave}\r\n            onDrop={handleDrop}\r\n          >\r\n            {!image && !isCameraActive && (\r\n              <div className=\"text-center space-y-6 w-full max-w-sm\">\r\n                <ImageIcon className=\"mx-auto h-16 w-16 text-muted-foreground\" />\r\n                <div className=\"space-y-2\">\r\n                  <p className=\"text-lg font-medium text-foreground\">\r\n                    {isDragOver ? \"Drop your image here\" : \"Upload your photo\"}\r\n                  </p>\r\n                  <p className=\"text-sm text-muted-foreground\">\r\n                    {isDragOver\r\n                      ? \"Release to upload\"\r\n                      : \"Drag and drop or click to select\"}\r\n                  </p>\r\n                </div>\r\n                <div className=\"flex flex-col sm:flex-row gap-3 w-full\">\r\n                  <Button\r\n                    onClick={() => fileInputRef.current?.click()}\r\n                    className=\"flex-1 h-11\"\r\n                  >\r\n                    <Upload className=\"mr-2 h-4 w-4\" /> Upload Image\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    onClick={() => setIsCameraActive(true)}\r\n                    className=\"flex-1 h-11\"\r\n                  >\r\n                    <Camera className=\"mr-2 h-4 w-4\" /> Use Camera\r\n                  </Button>\r\n                </div>\r\n                <Input\r\n                  id=\"post-image-upload\"\r\n                  name=\"post-image-upload\"\r\n                  ref={fileInputRef}\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  className=\"hidden\"\r\n                  onChange={handleImageUpload}\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {image && !isCameraActive && (\r\n              <div className=\"relative w-full h-full\">\r\n                <Image\r\n                  src={image}\r\n                  alt=\"Selected preview\"\r\n                  fill\r\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                  className=\"rounded-md object-contain\"\r\n                />\r\n                <Button\r\n                  variant=\"destructive\"\r\n                  size=\"icon\"\r\n                  className=\"absolute top-2 right-2 rounded-full h-8 w-8\"\r\n                  onClick={() => setImage(null)}\r\n                >\r\n                  <X className=\"h-4 w-4\" />\r\n                </Button>\r\n              </div>\r\n            )}\r\n\r\n            {isCameraActive && (\r\n              <div className=\"w-full h-full flex flex-col items-center justify-center space-y-4 p-4\">\r\n                <video\r\n                  ref={videoRef}\r\n                  className=\"w-full h-auto max-h-[20rem] rounded-lg shadow-lg\"\r\n                  autoPlay\r\n                  muted\r\n                  playsInline\r\n                />\r\n                <canvas ref={canvasRef} className=\"hidden\" />\r\n                {hasCameraPermission === false && (\r\n                  <Alert variant=\"destructive\" className=\"max-w-sm\">\r\n                    <AlertTitle>Camera Access Denied</AlertTitle>\r\n                    <AlertDescription>\r\n                      Please enable camera permissions in your browser settings\r\n                      to use this feature.\r\n                    </AlertDescription>\r\n                  </Alert>\r\n                )}\r\n                <div className=\"flex flex-col sm:flex-row gap-3 w-full max-w-sm\">\r\n                  <Button\r\n                    onClick={handleCapture}\r\n                    disabled={!hasCameraPermission}\r\n                    className=\"flex-1 h-11\"\r\n                  >\r\n                    Capture Photo\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    onClick={() => setIsCameraActive(false)}\r\n                    className=\"flex-1 h-11\"\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex items-center gap-4\">\r\n              <Avatar>\r\n                <AvatarImage src={user.avatarUrl} />\r\n                <AvatarFallback>\r\n                  {user.username.charAt(0).toUpperCase()}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <span className=\"font-semibold\">{user.username}</span>\r\n            </div>\r\n            <div>\r\n              <Label htmlFor=\"caption\" className=\"sr-only\">\r\n                Caption\r\n              </Label>\r\n              <Textarea\r\n                id=\"caption\"\r\n                autoComplete=\"off\"\r\n                placeholder=\"Write a caption...\"\r\n                value={caption}\r\n                onChange={(e) => setCaption(e.target.value)}\r\n                rows={10}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <DialogFooter>\r\n          <DialogClose asChild>\r\n            <Button type=\"button\" variant=\"secondary\">\r\n              Cancel\r\n            </Button>\r\n          </DialogClose>\r\n          <Button type=\"button\" onClick={handleSubmit}>\r\n            Share\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAtBA;;;;;;;;;;;;;;AA8Be,SAAS,cAAc,EACpC,MAAM,EACN,YAAY,EACZ,IAAI,EACe;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE3D;IACF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IAE/B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,SAAS,EAAE;YAClD,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,OAAO,SAAS,GAAG,OAAO,CAAC,CAAC,QAAU,MAAM,IAAI;YAChD,SAAS,OAAO,CAAC,SAAS,GAAG;QAC/B;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,MAAM,sBAAsB;gBAC1B,IAAI;oBACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;wBACvD,OAAO;oBACT;oBACA,uBAAuB;oBACvB,IAAI,SAAS,OAAO,EAAE;wBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;oBAC/B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2BAA2B;oBACzC,uBAAuB;oBACvB,MAAM;wBACJ,SAAS;wBACT,OAAO;wBACP,aACE;oBACJ;oBACA,kBAAkB;gBACpB;YACF;YACA;QACF,OAAO;YACL;QACF;QAEA,OAAO;YACL;QACF;IACF,GAAG;QAAC;QAAgB;QAAO;KAAc;IAEzC,MAAM,oBAAoB,CAAC;QACzB,IAAI,MAAM,MAAM,CAAC,KAAK,IAAI,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YAC/C,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC,IAAM,SAAS,EAAE,MAAM,EAAE;YAC1C,OAAO,aAAa,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE;YAC1C,kBAAkB;QACpB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAC1C,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC,IAAM,SAAS,EAAE,MAAM,EAAE;YAC1C,OAAO,aAAa,CAAC;YACrB,kBAAkB;QACpB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QACd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,SAAS,OAAO,IAAI,UAAU,OAAO,EAAE;YACzC,MAAM,QAAQ,SAAS,OAAO;YAC9B,MAAM,SAAS,UAAU,OAAO;YAChC,OAAO,KAAK,GAAG,MAAM,UAAU;YAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;YACjC,MAAM,UAAU,OAAO,UAAU,CAAC;YAClC,SAAS,UAAU,OAAO,GAAG,GAAG,MAAM,UAAU,EAAE,MAAM,WAAW;YACnE,MAAM,UAAU,OAAO,SAAS,CAAC;YACjC,SAAS;YACT,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,WAAW;QACX,kBAAkB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM;YACT;QACF;QACA,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO;YACV,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,IAAI;YACF,MAAM,UAAU;gBACd,QAAQ;oBACN,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS,IAAI;gBAC/B;gBACA,UAAU;gBACV,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,MAAM,QAAQ;YAEd,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAW,CAAC,kJAAkJ,EAC5J,aACI,8CACA,6FACJ;4BACF,YAAY;4BACZ,aAAa;4BACb,QAAQ;;gCAEP,CAAC,SAAS,CAAC,gCACV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,aAAa,yBAAyB;;;;;;8DAEzC,8OAAC;oDAAE,WAAU;8DACV,aACG,sBACA;;;;;;;;;;;;sDAGR,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,aAAa,OAAO,EAAE;oDACrC,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAErC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,kBAAkB;oDACjC,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAGvC,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,KAAK;4CACL,MAAK;4CACL,QAAO;4CACP,WAAU;4CACV,UAAU;;;;;;;;;;;;gCAKf,SAAS,CAAC,gCACT,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK;4CACL,KAAI;4CACJ,IAAI;4CACJ,OAAM;4CACN,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,SAAS;sDAExB,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAKlB,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK;4CACL,WAAU;4CACV,QAAQ;4CACR,KAAK;4CACL,WAAW;;;;;;sDAEb,8OAAC;4CAAO,KAAK;4CAAW,WAAU;;;;;;wCACjC,wBAAwB,uBACvB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;;8DACrC,8OAAC,iIAAA,CAAA,aAAU;8DAAC;;;;;;8DACZ,8OAAC,iIAAA,CAAA,mBAAgB;8DAAC;;;;;;;;;;;;sDAMtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,CAAC;oDACX,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,kBAAkB;oDACjC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;sCAQT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;;8DACL,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,SAAS;;;;;;8DAChC,8OAAC,kIAAA,CAAA,iBAAc;8DACZ,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;sDAGxC,8OAAC;4CAAK,WAAU;sDAAiB,KAAK,QAAQ;;;;;;;;;;;;8CAEhD,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAU;;;;;;sDAG7C,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,cAAa;4CACb,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8BAMd,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,OAAO;sCAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,SAAQ;0CAAY;;;;;;;;;;;sCAI5C,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,SAAS;sCAAc;;;;;;;;;;;;;;;;;;;;;;;AAOvD", "debugId": null}}, {"offset": {"line": 2404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Home, PlusSquare, Search, User as UserIcon } from 'lucide-react';\r\nimport { useAuth } from '@/hooks/use-auth';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Input } from './ui/input';\r\nimport NewPostDialog from './new-post-dialog';\r\nimport React from 'react';\r\n\r\nexport default function Header() {\r\n  const { user, signOut } = useAuth();\r\n  const router = useRouter();\r\n  const [isNewPostOpen, setIsNewPostOpen] = React.useState(false);\r\n\r\n  const handleLogout = () => {\r\n    signOut();\r\n    router.push('/login');\r\n  };\r\n  \r\n  const userInitial = user?.username ? user.username.charAt(0).toUpperCase() : '?';\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full border-b bg-card/80 backdrop-blur\">\r\n        <div className=\"container flex h-16 items-center justify-between max-w-5xl mx-auto px-4\">\r\n          <Link href=\"/\" className=\"text-2xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent\">\r\n            FireFrame\r\n          </Link>\r\n          <div className=\"hidden sm:flex flex-1 max-w-xs items-center relative\">\r\n             <Input placeholder=\"Search...\" className=\"pl-10\"/>\r\n             <Search className=\"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground\"/>\r\n          </div>\r\n          <nav className=\"flex items-center gap-2 sm:gap-4\">\r\n            <Button variant=\"ghost\" size=\"icon\" asChild>\r\n              <Link href=\"/\">\r\n                <Home className=\"h-6 w-6\" />\r\n                <span className=\"sr-only\">Home</span>\r\n              </Link>\r\n            </Button>\r\n            <Button variant=\"ghost\" size=\"icon\" onClick={() => setIsNewPostOpen(true)}>\r\n              <PlusSquare className=\"h-6 w-6\" />\r\n              <span className=\"sr-only\">New Post</span>\r\n            </Button>\r\n            {user && (\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button variant=\"ghost\" className=\"relative h-9 w-9 rounded-full\">\r\n                    <Avatar className=\"h-9 w-9\">\r\n                      <AvatarImage src={user.avatarUrl} alt={user.username} />\r\n                      <AvatarFallback>{userInitial}</AvatarFallback>\r\n                    </Avatar>\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\r\n                  <DropdownMenuLabel className=\"font-normal\">\r\n                    <div className=\"flex flex-col space-y-1\">\r\n                      <p className=\"text-sm font-medium leading-none\">{user.username}</p>\r\n                      <p className=\"text-xs leading-none text-muted-foreground\">\r\n                        {user.email}\r\n                      </p>\r\n                    </div>\r\n                  </DropdownMenuLabel>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem asChild>\r\n                    <Link href={`/profile/${user.username}`}>\r\n                      <UserIcon className=\"mr-2 h-4 w-4\" />\r\n                      <span>Profile</span>\r\n                    </Link>\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem onClick={handleLogout}>\r\n                    Log out\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            )}\r\n          </nav>\r\n        </div>\r\n      </header>\r\n      {user && <NewPostDialog isOpen={isNewPostOpen} onOpenChange={setIsNewPostOpen} user={user} />}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AAlBA;;;;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEzD,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,cAAc,MAAM,WAAW,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK;IAE7E,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAA0G;;;;;;sCAGnI,8OAAC;4BAAI,WAAU;;8CACZ,8OAAC,iIAAA,CAAA,QAAK;oCAAC,aAAY;oCAAY,WAAU;;;;;;8CACzC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;sCAErB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,OAAO;8CACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;8CAG9B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,SAAS,IAAM,iBAAiB;;sDAClE,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;gCAE3B,sBACC,8OAAC,4IAAA,CAAA,eAAY;;sDACX,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,OAAO;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,8OAAC,kIAAA,CAAA,cAAW;4DAAC,KAAK,KAAK,SAAS;4DAAE,KAAK,KAAK,QAAQ;;;;;;sEACpD,8OAAC,kIAAA,CAAA,iBAAc;sEAAE;;;;;;;;;;;;;;;;;;;;;;sDAIvB,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,WAAU;4CAAO,OAAM;4CAAM,UAAU;;8DAC1D,8OAAC,4IAAA,CAAA,oBAAiB;oDAAC,WAAU;8DAC3B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAoC,KAAK,QAAQ;;;;;;0EAC9D,8OAAC;gEAAE,WAAU;0EACV,KAAK,KAAK;;;;;;;;;;;;;;;;;8DAIjB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,OAAO;8DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,SAAS,EAAE,KAAK,QAAQ,EAAE;;0EACrC,8OAAC,kMAAA,CAAA,OAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,SAAS;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASpD,sBAAQ,8OAAC,2IAAA,CAAA,UAAa;gBAAC,QAAQ;gBAAe,cAAc;gBAAkB,MAAM;;;;;;;;AAG3F", "debugId": null}}, {"offset": {"line": 2717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2806, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/app/profile/%5Busername%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState, use } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Image from \"next/image\";\r\nimport { useAuth } from \"@/hooks/use-auth\";\r\nimport { supabase } from \"@/lib/supabase\";\r\nimport ProtectedRoute from \"@/components/auth/protected-route\";\r\nimport Header from \"@/components/header\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport {\r\n  Grid3x3,\r\n  Edit,\r\n  Mail,\r\n  Link as LinkIcon,\r\n  Phone,\r\n  MessageSquare,\r\n} from \"lucide-react\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogFooter,\r\n  DialogClose,\r\n  DialogDescription,\r\n} from \"@/components/ui/dialog\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport type { Post, User } from \"@/lib/types\";\r\nimport { usePostStore } from \"@/hooks/use-post-store\";\r\nimport { getPostsByUser, subscribeToUserPosts } from \"@/lib/supabase-posts\";\r\n\r\n// Mock posts for the profile page\r\nconst initialUserPosts: Post[] = Array.from({ length: 15 }).map((_, i) => ({\r\n  id: `post${i + 1}`,\r\n  author: {\r\n    username: \"testuser\",\r\n    avatarUrl: \"https://placehold.co/100x100.png\",\r\n  },\r\n  imageUrl: `https://placehold.co/300x300.png?i=${i}`,\r\n  caption: `This is post number ${i + 1}. A beautiful placeholder image.`,\r\n  likes: Math.floor(Math.random() * 200),\r\n  comments: Math.floor(Math.random() * 50),\r\n  createdAt: new Date(\r\n    Date.now() - i * 1000 * 60 * 60 * 24 * (Math.random() * 2 + 1)\r\n  ).toISOString(),\r\n}));\r\n\r\nexport default function ProfilePage({\r\n  params: paramsPromise,\r\n}: {\r\n  params: Promise<{ username: string }>;\r\n}) {\r\n  const params = use(paramsPromise);\r\n  const { isAuthenticated, user, isLoading, updateProfile, uploadAvatar } =\r\n    useAuth();\r\n  const router = useRouter();\r\n\r\n  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);\r\n  const [selectedPost, setSelectedPost] = useState<Post | null>(null);\r\n  const [editedCaption, setEditedCaption] = useState(\"\");\r\n  const [editedImageUrl, setEditedImageUrl] = useState<string | null>(null);\r\n\r\n  const [isEditProfileDialogOpen, setIsEditProfileDialogOpen] = useState(false);\r\n  const [editedProfile, setEditedProfile] = useState<Partial<User>>({});\r\n\r\n  const { posts: allPosts, updatePost: updateStorePost } = usePostStore();\r\n  const [userPosts, setUserPosts] = useState<Post[]>([]);\r\n\r\n  const [filteredPosts, setFilteredPosts] = useState<Post[]>([]);\r\n  const [titleFilter, setTitleFilter] = useState(\"\");\r\n  const [dateFilter, setDateFilter] = useState(\"\");\r\n\r\n  // Move these state declarations to the top to avoid reference errors\r\n  const [profileUser, setProfileUser] = useState<User | null>(null);\r\n  const [profileLoading, setProfileLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // Fetch user posts from Firebase\r\n    const fetchUserPosts = async () => {\r\n      try {\r\n        if (params.username === \"testuser\") {\r\n          // Keep mock posts for testuser\r\n          const postsWithUsername = initialUserPosts.map((p) => ({\r\n            ...p,\r\n            author: { ...p.author, username: params.username },\r\n          }));\r\n          setUserPosts(postsWithUsername);\r\n        } else {\r\n          // Fetch real posts from Firebase\r\n          const posts = await getPostsByUser(params.username);\r\n          setUserPosts(posts);\r\n\r\n          // Set up real-time listener for user posts\r\n          const unsubscribe = subscribeToUserPosts(params.username, (posts) => {\r\n            setUserPosts(posts);\r\n          });\r\n\r\n          // Cleanup function\r\n          return () => unsubscribe();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching user posts:\", error);\r\n        setUserPosts([]);\r\n      }\r\n    };\r\n\r\n    fetchUserPosts();\r\n  }, [params.username]);\r\n\r\n  useEffect(() => {\r\n    if (!isLoading && !isAuthenticated) {\r\n      router.push(\"/login\");\r\n    }\r\n  }, [isAuthenticated, isLoading, router]);\r\n\r\n  useEffect(() => {\r\n    let result = userPosts;\r\n    if (titleFilter) {\r\n      result = result.filter((post) =>\r\n        post.caption.toLowerCase().includes(titleFilter.toLowerCase())\r\n      );\r\n    }\r\n    if (dateFilter) {\r\n      result = result.filter((post) => post.createdAt?.startsWith(dateFilter));\r\n    }\r\n    setFilteredPosts(result);\r\n  }, [titleFilter, dateFilter, userPosts]);\r\n\r\n  const isOwnProfile = user?.username === params.username;\r\n\r\n  // Load profile data - MOVED BEFORE EARLY RETURN TO FIX HOOKS ERROR\r\n  useEffect(() => {\r\n    const loadProfile = async () => {\r\n      // If this is the user's own profile and we have user data\r\n      if (isOwnProfile && user) {\r\n        // Only update if we don't already have the profile data or if the user data changed\r\n        if (!profileUser || profileUser.id !== user.id) {\r\n          console.log(\"👤 Loading own profile:\", user);\r\n          setProfileUser(user);\r\n        }\r\n        setProfileLoading(false);\r\n        return;\r\n      }\r\n\r\n      // Don't clear profileUser if we're waiting for user data to load\r\n      if (isOwnProfile && !user && isLoading) {\r\n        console.log(\"⏳ Waiting for user data to load...\");\r\n        return;\r\n      }\r\n\r\n      // If this is someone else's profile, load from database\r\n      if (!isOwnProfile) {\r\n        // Only load if we don't already have the profile data for this username\r\n        if (!profileUser || profileUser.username !== params.username) {\r\n          setProfileLoading(true);\r\n          try {\r\n            const { data, error } = await supabase\r\n              .from(\"users\")\r\n              .select(\"*\")\r\n              .eq(\"username\", params.username)\r\n              .single();\r\n\r\n            if (error) {\r\n              console.error(\"Error loading profile:\", error);\r\n              // Create a fallback profile\r\n              setProfileUser({\r\n                id: \"unknown\",\r\n                username: params.username,\r\n                email: \"<EMAIL>\",\r\n                avatarUrl: \"https://placehold.co/150x150.png\",\r\n                bio: `This is the profile of ${params.username}.`,\r\n                contacts: {\r\n                  website: { value: \"\", isPublic: false },\r\n                  phone: { value: \"\", isPublic: false },\r\n                  messaging: { platform: \"\", username: \"\", isPublic: false },\r\n                },\r\n              });\r\n            } else {\r\n              setProfileUser({\r\n                id: data.id,\r\n                username: data.username,\r\n                email: data.email,\r\n                avatarUrl: data.avatar_url,\r\n                bio: data.bio,\r\n                contacts: {\r\n                  website: {\r\n                    value: data.website_url || \"\",\r\n                    isPublic: data.website_public || false,\r\n                  },\r\n                  phone: {\r\n                    value: data.phone || \"\",\r\n                    isPublic: data.phone_public || false,\r\n                  },\r\n                  messaging: {\r\n                    platform: data.messaging_platform || \"\",\r\n                    username: data.messaging_username || \"\",\r\n                    isPublic: data.messaging_public || false,\r\n                  },\r\n                },\r\n              });\r\n            }\r\n          } catch (error) {\r\n            console.error(\"Error loading profile:\", error);\r\n          } finally {\r\n            setProfileLoading(false);\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // Only load profile when we have the necessary data\r\n    if (!isLoading && (user || !isOwnProfile)) {\r\n      console.log(\r\n        \"🔄 Loading profile - isOwnProfile:\",\r\n        isOwnProfile,\r\n        \"user:\",\r\n        !!user,\r\n        \"isLoading:\",\r\n        isLoading\r\n      );\r\n      loadProfile();\r\n    } else {\r\n      console.log(\r\n        \"⏸️ Skipping profile load - isOwnProfile:\",\r\n        isOwnProfile,\r\n        \"user:\",\r\n        !!user,\r\n        \"isLoading:\",\r\n        isLoading\r\n      );\r\n    }\r\n  }, [user, params.username, isOwnProfile, isLoading]);\r\n\r\n  // Debug useEffect to track profileUser changes\r\n  useEffect(() => {\r\n    console.log(\r\n      \"🔍 ProfileUser state changed:\",\r\n      profileUser ? \"has data\" : \"null/undefined\"\r\n    );\r\n    if (profileUser) {\r\n      console.log(\"📊 ProfileUser data:\", {\r\n        username: profileUser.username,\r\n        avatarUrl: !!profileUser.avatarUrl,\r\n        bio: !!profileUser.bio,\r\n      });\r\n    }\r\n  }, [profileUser]);\r\n\r\n  const handleEditClick = (post: Post) => {\r\n    if (user?.username === params.username) {\r\n      setSelectedPost(post);\r\n      setEditedCaption(post.caption);\r\n      setEditedImageUrl(post.imageUrl);\r\n      setIsEditDialogOpen(true);\r\n    }\r\n  };\r\n\r\n  const handleSaveChanges = async () => {\r\n    if (selectedPost) {\r\n      try {\r\n        const updatedPost = {\r\n          ...selectedPost,\r\n          caption: editedCaption,\r\n          imageUrl: editedImageUrl || selectedPost.imageUrl,\r\n        };\r\n\r\n        // Update in Firebase (this will trigger the real-time listener)\r\n        await updateStorePost(updatedPost);\r\n\r\n        setIsEditDialogOpen(false);\r\n        setSelectedPost(null);\r\n        setEditedImageUrl(null);\r\n      } catch (error) {\r\n        console.error(\"Error updating post:\", error);\r\n        // You could show an error toast here\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleEditProfileOpen = () => {\r\n    if (user) {\r\n      setEditedProfile(user);\r\n      setIsEditProfileDialogOpen(true);\r\n    }\r\n  };\r\n\r\n  const handleProfileChangesSave = async () => {\r\n    try {\r\n      console.log(\"🔄 Saving profile changes:\", editedProfile);\r\n\r\n      // Store current profileUser before update to prevent loss\r\n      const currentProfileUser = profileUser;\r\n\r\n      const { error } = await updateProfile(editedProfile);\r\n      if (error) {\r\n        console.error(\"❌ Error updating profile:\", error);\r\n        // You could show an error toast here\r\n        return;\r\n      }\r\n\r\n      console.log(\"✅ Profile updated successfully\");\r\n\r\n      // Update the local profileUser state immediately after successful update\r\n      // Use currentProfileUser as base to ensure we don't lose data\r\n      if (isOwnProfile && currentProfileUser) {\r\n        const updatedProfile = { ...currentProfileUser, ...editedProfile };\r\n        console.log(\"🔄 Updating local profileUser state:\", updatedProfile);\r\n        setProfileUser(updatedProfile);\r\n      }\r\n\r\n      setIsEditProfileDialogOpen(false);\r\n    } catch (error) {\r\n      console.error(\"❌ Exception updating profile:\", error);\r\n    }\r\n  };\r\n\r\n  const handleProfileInputChange = (field: keyof User, value: any) => {\r\n    setEditedProfile((prev) => ({ ...prev, [field]: value }));\r\n  };\r\n\r\n  const handleContactChange = (\r\n    contact: keyof NonNullable<User[\"contacts\"]>,\r\n    field: string,\r\n    value: any\r\n  ) => {\r\n    setEditedProfile((prev) => ({\r\n      ...prev,\r\n      contacts: {\r\n        ...prev.contacts,\r\n        [contact]: {\r\n          // @ts-ignore\r\n          ...prev.contacts?.[contact],\r\n          [field]: value,\r\n        },\r\n      },\r\n    }));\r\n  };\r\n\r\n  if (isLoading || !isAuthenticated || profileLoading) {\r\n    return (\r\n      <div className=\"flex flex-col h-screen w-full bg-background\">\r\n        <header className=\"flex items-center justify-between p-4 border-b\">\r\n          <Skeleton className=\"h-8 w-32\" />\r\n          <div className=\"flex items-center gap-4\">\r\n            <Skeleton className=\"h-8 w-8 rounded-full\" />\r\n            <Skeleton className=\"h-8 w-8 rounded-full\" />\r\n            <Skeleton className=\"h-8 w-8 rounded-full\" />\r\n          </div>\r\n        </header>\r\n        <main className=\"flex-1 p-4 md:p-8 overflow-y-auto\">\r\n          <div className=\"max-w-4xl mx-auto\">\r\n            <div className=\"flex items-center gap-8\">\r\n              <Skeleton className=\"h-36 w-36 rounded-full\" />\r\n              <div className=\"space-y-4 flex-1\">\r\n                <Skeleton className=\"h-8 w-48\" />\r\n                <div className=\"flex gap-8\">\r\n                  <Skeleton className=\"h-6 w-24\" />\r\n                  <Skeleton className=\"h-6 w-24\" />\r\n                  <Skeleton className=\"h-6 w-24\" />\r\n                </div>\r\n                <Skeleton className=\"h-5 w-32\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const stats = [\r\n    { label: \"posts\", value: userPosts.length },\r\n    { label: \"followers\", value: \"1.2k\" },\r\n    { label: \"following\", value: \"342\" },\r\n  ];\r\n\r\n  return (\r\n    <ProtectedRoute>\r\n      <div className=\"flex flex-col min-h-screen bg-background\">\r\n        <Header />\r\n        <main className=\"flex-1 w-full max-w-4xl mx-auto py-8 px-4\">\r\n          {(() => {\r\n            if (!profileUser) {\r\n              console.log(\"⚠️ ProfileUser is null/undefined, showing skeleton\");\r\n            }\r\n            return null;\r\n          })()}\r\n          {!profileUser ? (\r\n            // Show loading skeleton if profileUser is not loaded yet\r\n            <div className=\"flex flex-col md:flex-row items-center gap-8 md:gap-16 mb-12\">\r\n              <Skeleton className=\"h-36 w-36 rounded-full\" />\r\n              <div className=\"space-y-4 flex-1\">\r\n                <Skeleton className=\"h-8 w-48\" />\r\n                <div className=\"flex gap-8\">\r\n                  <Skeleton className=\"h-6 w-24\" />\r\n                  <Skeleton className=\"h-6 w-24\" />\r\n                  <Skeleton className=\"h-6 w-24\" />\r\n                </div>\r\n                <Skeleton className=\"h-5 w-32\" />\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <div className=\"flex flex-col md:flex-row items-center gap-8 md:gap-16 mb-12\">\r\n                <Avatar className=\"h-36 w-36 border-4 border-background ring-2 ring-primary\">\r\n                  <AvatarImage src={profileUser?.avatarUrl} />\r\n                  <AvatarFallback>\r\n                    {params.username.charAt(0).toUpperCase()}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center gap-4\">\r\n                    <h1 className=\"text-2xl font-light\">{params.username}</h1>\r\n                    {isOwnProfile && (\r\n                      <Button\r\n                        variant=\"secondary\"\r\n                        onClick={handleEditProfileOpen}\r\n                      >\r\n                        Edit Profile\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"flex items-center gap-8\">\r\n                    {stats.map((stat) => (\r\n                      <div\r\n                        key={stat.label}\r\n                        className=\"text-center md:text-left\"\r\n                      >\r\n                        <span className=\"font-semibold\">{stat.value}</span>\r\n                        <span className=\"text-muted-foreground ml-1\">\r\n                          {stat.label}\r\n                        </span>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"font-semibold\">{profileUser?.username}</p>\r\n                    <p className=\"text-muted-foreground text-sm\">\r\n                      {profileUser?.bio}\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"flex flex-col items-start gap-2 text-sm\">\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Mail size={16} className=\"text-muted-foreground\" />\r\n                      <a\r\n                        href={`mailto:${profileUser?.email}`}\r\n                        className=\"hover:underline\"\r\n                      >\r\n                        {profileUser?.email}\r\n                      </a>\r\n                    </div>\r\n                    {profileUser?.contacts?.website?.isPublic && (\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <LinkIcon size={16} className=\"text-muted-foreground\" />\r\n                        <a\r\n                          href={profileUser.contacts.website.value}\r\n                          target=\"_blank\"\r\n                          rel=\"noopener noreferrer\"\r\n                          className=\"hover:underline\"\r\n                        >\r\n                          {profileUser.contacts.website.value}\r\n                        </a>\r\n                      </div>\r\n                    )}\r\n                    {profileUser?.contacts?.phone?.isPublic && (\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Phone size={16} className=\"text-muted-foreground\" />\r\n                        <span>{profileUser.contacts.phone.value}</span>\r\n                      </div>\r\n                    )}\r\n                    {profileUser?.contacts?.messaging?.isPublic && (\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <MessageSquare\r\n                          size={16}\r\n                          className=\"text-muted-foreground\"\r\n                        />\r\n                        <span>\r\n                          {profileUser.contacts.messaging.platform}:{\" \"}\r\n                          {profileUser.contacts.messaging.username}\r\n                        </span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <Tabs defaultValue=\"posts\" className=\"w-full\">\r\n                <TabsList className=\"border-t w-full justify-center rounded-none bg-transparent pt-2\">\r\n                  <TabsTrigger\r\n                    value=\"posts\"\r\n                    className=\"gap-2 data-[state=active]:border-t-2 data-[state=active]:border-primary data-[state=active]:text-primary rounded-none\"\r\n                  >\r\n                    <Grid3x3 size={16} /> POSTS\r\n                  </TabsTrigger>\r\n                </TabsList>\r\n                <TabsContent value=\"posts\">\r\n                  {isOwnProfile && (\r\n                    <div className=\"flex items-center gap-4 my-4 p-4 border rounded-lg bg-card\">\r\n                      <div className=\"flex flex-col gap-1\">\r\n                        <Label\r\n                          htmlFor=\"caption-filter\"\r\n                          className=\"text-xs text-muted-foreground\"\r\n                        >\r\n                          Filter by caption\r\n                        </Label>\r\n                        <Input\r\n                          id=\"caption-filter\"\r\n                          name=\"caption-filter\"\r\n                          type=\"text\"\r\n                          placeholder=\"Filter by caption...\"\r\n                          className=\"max-w-sm\"\r\n                          value={titleFilter}\r\n                          onChange={(e) => setTitleFilter(e.target.value)}\r\n                        />\r\n                      </div>\r\n                      <div className=\"flex flex-col gap-1\">\r\n                        <Label\r\n                          htmlFor=\"date-filter\"\r\n                          className=\"text-xs text-muted-foreground\"\r\n                        >\r\n                          Filter by date\r\n                        </Label>\r\n                        <Input\r\n                          id=\"date-filter\"\r\n                          name=\"date-filter\"\r\n                          type=\"date\"\r\n                          className=\"max-w-sm\"\r\n                          value={dateFilter}\r\n                          onChange={(e) => setDateFilter(e.target.value)}\r\n                        />\r\n                      </div>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        onClick={() => {\r\n                          setTitleFilter(\"\");\r\n                          setDateFilter(\"\");\r\n                        }}\r\n                      >\r\n                        Clear\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                  <div className=\"grid grid-cols-3 gap-1 md:gap-4\">\r\n                    {filteredPosts.map((post) => (\r\n                      <div\r\n                        key={post.id}\r\n                        className=\"relative aspect-square group\"\r\n                      >\r\n                        <Image\r\n                          src={post.imageUrl}\r\n                          alt={`Post by ${post.author.username}`}\r\n                          fill\r\n                          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                          className=\"object-cover rounded-md\"\r\n                          data-ai-hint=\"travel landscape\"\r\n                        />\r\n                        {isOwnProfile && (\r\n                          <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-md\">\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              className=\"text-white hover:bg-white/20\"\r\n                              onClick={() => handleEditClick(post)}\r\n                            >\r\n                              <Edit size={24} />\r\n                            </Button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </TabsContent>\r\n              </Tabs>\r\n            </>\r\n          )}\r\n        </main>\r\n\r\n        {/* Edit Post Dialog */}\r\n        {selectedPost && (\r\n          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>\r\n            <DialogContent className=\"max-h-[90vh] overflow-y-auto max-w-2xl\">\r\n              <DialogHeader>\r\n                <DialogTitle>Edit Post</DialogTitle>\r\n                <DialogDescription>\r\n                  Make changes to your post here. Click save when you're done.\r\n                </DialogDescription>\r\n              </DialogHeader>\r\n              <div className=\"space-y-4 py-4\">\r\n                <div className=\"space-y-2\">\r\n                  <Label>Image</Label>\r\n                  <div className=\"relative aspect-square\">\r\n                    <Image\r\n                      src={editedImageUrl || selectedPost.imageUrl}\r\n                      alt=\"Current post image\"\r\n                      fill\r\n                      className=\"object-cover rounded-md\"\r\n                    />\r\n                  </div>\r\n                  <Input\r\n                    type=\"file\"\r\n                    id=\"image\"\r\n                    className=\"mt-2\"\r\n                    accept=\"image/*\"\r\n                    onChange={(e) => {\r\n                      if (e.target.files && e.target.files[0]) {\r\n                        const reader = new FileReader();\r\n                        reader.onload = (event) => {\r\n                          setEditedImageUrl(event.target?.result as string);\r\n                        };\r\n                        reader.readAsDataURL(e.target.files[0]);\r\n                      }\r\n                    }}\r\n                  />\r\n                </div>\r\n                <div className=\"space-y-2\">\r\n                  <Label htmlFor=\"caption\">Caption</Label>\r\n                  <Textarea\r\n                    id=\"caption\"\r\n                    value={editedCaption}\r\n                    onChange={(e) => setEditedCaption(e.target.value)}\r\n                  />\r\n                </div>\r\n                <div className=\"text-xs text-muted-foreground\">\r\n                  <p>User ID: {selectedPost.author.username} (not editable)</p>\r\n                  <p>\r\n                    Date:{\" \"}\r\n                    {new Date(selectedPost.createdAt!).toLocaleDateString()}{\" \"}\r\n                    (not editable)\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <DialogFooter>\r\n                <DialogClose asChild>\r\n                  <Button type=\"button\" variant=\"secondary\">\r\n                    Cancel\r\n                  </Button>\r\n                </DialogClose>\r\n                <Button type=\"button\" onClick={handleSaveChanges}>\r\n                  Save Changes\r\n                </Button>\r\n              </DialogFooter>\r\n            </DialogContent>\r\n          </Dialog>\r\n        )}\r\n\r\n        {/* Edit Profile Dialog */}\r\n        <Dialog\r\n          open={isEditProfileDialogOpen}\r\n          onOpenChange={setIsEditProfileDialogOpen}\r\n        >\r\n          <DialogContent className=\"max-h-[90vh] overflow-y-auto\">\r\n            <DialogHeader>\r\n              <DialogTitle>Edit Profile</DialogTitle>\r\n              <DialogDescription>\r\n                Make changes to your profile here. Click save when you're done.\r\n              </DialogDescription>\r\n            </DialogHeader>\r\n            <div className=\"space-y-6 py-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label>Profile Picture</Label>\r\n                <div className=\"flex items-center gap-4\">\r\n                  <Avatar className=\"h-20 w-20\">\r\n                    <AvatarImage src={editedProfile.avatarUrl} />\r\n                    <AvatarFallback>\r\n                      {editedProfile.username?.charAt(0).toUpperCase()}\r\n                    </AvatarFallback>\r\n                  </Avatar>\r\n                  <Input\r\n                    type=\"file\"\r\n                    id=\"avatar\"\r\n                    accept=\"image/*\"\r\n                    onChange={async (e) => {\r\n                      if (e.target.files && e.target.files[0]) {\r\n                        const file = e.target.files[0];\r\n\r\n                        // Show preview immediately\r\n                        const reader = new FileReader();\r\n                        reader.onload = (event) => {\r\n                          handleProfileInputChange(\r\n                            \"avatarUrl\",\r\n                            event.target?.result as string\r\n                          );\r\n                        };\r\n                        reader.readAsDataURL(file);\r\n\r\n                        // Upload to Supabase storage\r\n                        try {\r\n                          console.log(\"📤 Uploading avatar...\");\r\n                          const { url, error } = await uploadAvatar(file);\r\n                          if (error) {\r\n                            console.error(\"❌ Error uploading avatar:\", error);\r\n                            // You could show an error toast here\r\n                          } else if (url) {\r\n                            console.log(\r\n                              \"✅ Avatar uploaded successfully:\",\r\n                              url\r\n                            );\r\n                            handleProfileInputChange(\"avatarUrl\", url);\r\n                            // Update profileUser state immediately since uploadAvatar already updated the database\r\n                            if (isOwnProfile) {\r\n                              console.log(\r\n                                \"🔄 Updating profileUser with new avatar\"\r\n                              );\r\n                              setProfileUser((prev) =>\r\n                                prev ? { ...prev, avatarUrl: url } : null\r\n                              );\r\n                            }\r\n                          }\r\n                        } catch (error) {\r\n                          console.error(\r\n                            \"❌ Exception uploading avatar:\",\r\n                            error\r\n                          );\r\n                        }\r\n                      }\r\n                    }}\r\n                  />\r\n                </div>\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"bio\">Bio</Label>\r\n                <Textarea\r\n                  id=\"bio\"\r\n                  autoComplete=\"off\"\r\n                  value={editedProfile.bio || \"\"}\r\n                  onChange={(e) =>\r\n                    handleProfileInputChange(\"bio\", e.target.value)\r\n                  }\r\n                />\r\n              </div>\r\n\r\n              <h4 className=\"font-semibold text-lg border-t pt-4\">\r\n                Contact Information\r\n              </h4>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"email\">Email</Label>\r\n                <Input\r\n                  id=\"email\"\r\n                  value={editedProfile.email || \"\"}\r\n                  disabled\r\n                  className=\"bg-muted\"\r\n                />\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  Email is always public and cannot be changed here.\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"space-y-4 p-4 border rounded-md\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <Label htmlFor=\"website\">Website</Label>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Switch\r\n                      id=\"website-public\"\r\n                      checked={editedProfile.contacts?.website?.isPublic}\r\n                      onCheckedChange={(checked) =>\r\n                        handleContactChange(\"website\", \"isPublic\", checked)\r\n                      }\r\n                    />\r\n                    <Label htmlFor=\"website-public\" className=\"text-xs\">\r\n                      {editedProfile.contacts?.website?.isPublic\r\n                        ? \"Public\"\r\n                        : \"Private\"}\r\n                    </Label>\r\n                  </div>\r\n                </div>\r\n                <Input\r\n                  id=\"website\"\r\n                  type=\"url\"\r\n                  autoComplete=\"url\"\r\n                  value={editedProfile.contacts?.website?.value || \"\"}\r\n                  onChange={(e) =>\r\n                    handleContactChange(\"website\", \"value\", e.target.value)\r\n                  }\r\n                  placeholder=\"https://your-website.com\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"space-y-4 p-4 border rounded-md\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <Label htmlFor=\"phone\">Phone Number</Label>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Switch\r\n                      id=\"phone-public\"\r\n                      checked={editedProfile.contacts?.phone?.isPublic}\r\n                      onCheckedChange={(checked) =>\r\n                        handleContactChange(\"phone\", \"isPublic\", checked)\r\n                      }\r\n                    />\r\n                    <Label htmlFor=\"phone-public\" className=\"text-xs\">\r\n                      {editedProfile.contacts?.phone?.isPublic\r\n                        ? \"Public\"\r\n                        : \"Private\"}\r\n                    </Label>\r\n                  </div>\r\n                </div>\r\n                <Input\r\n                  id=\"phone\"\r\n                  type=\"tel\"\r\n                  autoComplete=\"tel\"\r\n                  value={editedProfile.contacts?.phone?.value || \"\"}\r\n                  onChange={(e) =>\r\n                    handleContactChange(\"phone\", \"value\", e.target.value)\r\n                  }\r\n                  placeholder=\"************\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"space-y-4 p-4 border rounded-md\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <Label htmlFor=\"messaging\">Messaging</Label>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Switch\r\n                      id=\"messaging-public\"\r\n                      checked={editedProfile.contacts?.messaging?.isPublic}\r\n                      onCheckedChange={(checked) =>\r\n                        handleContactChange(\"messaging\", \"isPublic\", checked)\r\n                      }\r\n                    />\r\n                    <Label htmlFor=\"messaging-public\" className=\"text-xs\">\r\n                      {editedProfile.contacts?.messaging?.isPublic\r\n                        ? \"Public\"\r\n                        : \"Private\"}\r\n                    </Label>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex gap-2\">\r\n                  <Input\r\n                    id=\"messaging-platform\"\r\n                    type=\"text\"\r\n                    autoComplete=\"off\"\r\n                    value={editedProfile.contacts?.messaging?.platform || \"\"}\r\n                    onChange={(e) =>\r\n                      handleContactChange(\r\n                        \"messaging\",\r\n                        \"platform\",\r\n                        e.target.value\r\n                      )\r\n                    }\r\n                    placeholder=\"Platform (e.g., Telegram)\"\r\n                  />\r\n                  <Input\r\n                    id=\"messaging-username\"\r\n                    type=\"text\"\r\n                    autoComplete=\"off\"\r\n                    value={editedProfile.contacts?.messaging?.username || \"\"}\r\n                    onChange={(e) =>\r\n                      handleContactChange(\r\n                        \"messaging\",\r\n                        \"username\",\r\n                        e.target.value\r\n                      )\r\n                    }\r\n                    placeholder=\"@username\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <DialogFooter>\r\n              <DialogClose asChild>\r\n                <Button type=\"button\" variant=\"secondary\">\r\n                  Cancel\r\n                </Button>\r\n              </DialogClose>\r\n              <Button type=\"button\" onClick={handleProfileChangesSave}>\r\n                Save Changes\r\n              </Button>\r\n            </DialogFooter>\r\n          </DialogContent>\r\n        </Dialog>\r\n      </div>\r\n    </ProtectedRoute>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AASA;AACA;AACA;AACA;AAEA;AACA;AApCA;;;;;;;;;;;;;;;;;;;;;AAsCA,kCAAkC;AAClC,MAAM,mBAA2B,MAAM,IAAI,CAAC;IAAE,QAAQ;AAAG,GAAG,GAAG,CAAC,CAAC,GAAG,IAAM,CAAC;QACzE,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG;QAClB,QAAQ;YACN,UAAU;YACV,WAAW;QACb;QACA,UAAU,CAAC,mCAAmC,EAAE,GAAG;QACnD,SAAS,CAAC,oBAAoB,EAAE,IAAI,EAAE,gCAAgC,CAAC;QACvE,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAClC,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QACrC,WAAW,IAAI,KACb,KAAK,GAAG,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,GAC7D,WAAW;IACf,CAAC;AAEc,SAAS,YAAY,EAClC,QAAQ,aAAa,EAGtB;IACC,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,MAAG,AAAD,EAAE;IACnB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,GACrE,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACR,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,CAAC;IAEnE,MAAM,EAAE,OAAO,QAAQ,EAAE,YAAY,eAAe,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAErD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qEAAqE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iCAAiC;QACjC,MAAM,iBAAiB;YACrB,IAAI;gBACF,IAAI,OAAO,QAAQ,KAAK,YAAY;oBAClC,+BAA+B;oBAC/B,MAAM,oBAAoB,iBAAiB,GAAG,CAAC,CAAC,IAAM,CAAC;4BACrD,GAAG,CAAC;4BACJ,QAAQ;gCAAE,GAAG,EAAE,MAAM;gCAAE,UAAU,OAAO,QAAQ;4BAAC;wBACnD,CAAC;oBACD,aAAa;gBACf,OAAO;oBACL,iCAAiC;oBACjC,MAAM,QAAQ,MAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,QAAQ;oBAClD,aAAa;oBAEb,2CAA2C;oBAC3C,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,QAAQ,EAAE,CAAC;wBACzD,aAAa;oBACf;oBAEA,mBAAmB;oBACnB,OAAO,IAAM;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,aAAa,EAAE;YACjB;QACF;QAEA;IACF,GAAG;QAAC,OAAO,QAAQ;KAAC;IAEpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;QACb,IAAI,aAAa;YACf,SAAS,OAAO,MAAM,CAAC,CAAC,OACtB,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAE/D;QACA,IAAI,YAAY;YACd,SAAS,OAAO,MAAM,CAAC,CAAC,OAAS,KAAK,SAAS,EAAE,WAAW;QAC9D;QACA,iBAAiB;IACnB,GAAG;QAAC;QAAa;QAAY;KAAU;IAEvC,MAAM,eAAe,MAAM,aAAa,OAAO,QAAQ;IAEvD,mEAAmE;IACnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,0DAA0D;YAC1D,IAAI,gBAAgB,MAAM;gBACxB,oFAAoF;gBACpF,IAAI,CAAC,eAAe,YAAY,EAAE,KAAK,KAAK,EAAE,EAAE;oBAC9C,QAAQ,GAAG,CAAC,2BAA2B;oBACvC,eAAe;gBACjB;gBACA,kBAAkB;gBAClB;YACF;YAEA,iEAAiE;YACjE,IAAI,gBAAgB,CAAC,QAAQ,WAAW;gBACtC,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,wDAAwD;YACxD,IAAI,CAAC,cAAc;gBACjB,wEAAwE;gBACxE,IAAI,CAAC,eAAe,YAAY,QAAQ,KAAK,OAAO,QAAQ,EAAE;oBAC5D,kBAAkB;oBAClB,IAAI;wBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,OAAO,QAAQ,EAC9B,MAAM;wBAET,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,0BAA0B;4BACxC,4BAA4B;4BAC5B,eAAe;gCACb,IAAI;gCACJ,UAAU,OAAO,QAAQ;gCACzB,OAAO;gCACP,WAAW;gCACX,KAAK,CAAC,uBAAuB,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC;gCACjD,UAAU;oCACR,SAAS;wCAAE,OAAO;wCAAI,UAAU;oCAAM;oCACtC,OAAO;wCAAE,OAAO;wCAAI,UAAU;oCAAM;oCACpC,WAAW;wCAAE,UAAU;wCAAI,UAAU;wCAAI,UAAU;oCAAM;gCAC3D;4BACF;wBACF,OAAO;4BACL,eAAe;gCACb,IAAI,KAAK,EAAE;gCACX,UAAU,KAAK,QAAQ;gCACvB,OAAO,KAAK,KAAK;gCACjB,WAAW,KAAK,UAAU;gCAC1B,KAAK,KAAK,GAAG;gCACb,UAAU;oCACR,SAAS;wCACP,OAAO,KAAK,WAAW,IAAI;wCAC3B,UAAU,KAAK,cAAc,IAAI;oCACnC;oCACA,OAAO;wCACL,OAAO,KAAK,KAAK,IAAI;wCACrB,UAAU,KAAK,YAAY,IAAI;oCACjC;oCACA,WAAW;wCACT,UAAU,KAAK,kBAAkB,IAAI;wCACrC,UAAU,KAAK,kBAAkB,IAAI;wCACrC,UAAU,KAAK,gBAAgB,IAAI;oCACrC;gCACF;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0BAA0B;oBAC1C,SAAU;wBACR,kBAAkB;oBACpB;gBACF;YACF;QACF;QAEA,oDAAoD;QACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,GAAG;YACzC,QAAQ,GAAG,CACT,sCACA,cACA,SACA,CAAC,CAAC,MACF,cACA;YAEF;QACF,OAAO;YACL,QAAQ,GAAG,CACT,4CACA,cACA,SACA,CAAC,CAAC,MACF,cACA;QAEJ;IACF,GAAG;QAAC;QAAM,OAAO,QAAQ;QAAE;QAAc;KAAU;IAEnD,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CACT,iCACA,cAAc,aAAa;QAE7B,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC,wBAAwB;gBAClC,UAAU,YAAY,QAAQ;gBAC9B,WAAW,CAAC,CAAC,YAAY,SAAS;gBAClC,KAAK,CAAC,CAAC,YAAY,GAAG;YACxB;QACF;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM,aAAa,OAAO,QAAQ,EAAE;YACtC,gBAAgB;YAChB,iBAAiB,KAAK,OAAO;YAC7B,kBAAkB,KAAK,QAAQ;YAC/B,oBAAoB;QACtB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,cAAc;YAChB,IAAI;gBACF,MAAM,cAAc;oBAClB,GAAG,YAAY;oBACf,SAAS;oBACT,UAAU,kBAAkB,aAAa,QAAQ;gBACnD;gBAEA,gEAAgE;gBAChE,MAAM,gBAAgB;gBAEtB,oBAAoB;gBACpB,gBAAgB;gBAChB,kBAAkB;YACpB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,qCAAqC;YACvC;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,MAAM;YACR,iBAAiB;YACjB,2BAA2B;QAC7B;IACF;IAEA,MAAM,2BAA2B;QAC/B,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,0DAA0D;YAC1D,MAAM,qBAAqB;YAE3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,cAAc;YACtC,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,qCAAqC;gBACrC;YACF;YAEA,QAAQ,GAAG,CAAC;YAEZ,yEAAyE;YACzE,8DAA8D;YAC9D,IAAI,gBAAgB,oBAAoB;gBACtC,MAAM,iBAAiB;oBAAE,GAAG,kBAAkB;oBAAE,GAAG,aAAa;gBAAC;gBACjE,QAAQ,GAAG,CAAC,wCAAwC;gBACpD,eAAe;YACjB;YAEA,2BAA2B;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,2BAA2B,CAAC,OAAmB;QACnD,iBAAiB,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IACzD;IAEA,MAAM,sBAAsB,CAC1B,SACA,OACA;QAEA,iBAAiB,CAAC,OAAS,CAAC;gBAC1B,GAAG,IAAI;gBACP,UAAU;oBACR,GAAG,KAAK,QAAQ;oBAChB,CAAC,QAAQ,EAAE;wBACT,aAAa;wBACb,GAAG,KAAK,QAAQ,EAAE,CAAC,QAAQ;wBAC3B,CAAC,MAAM,EAAE;oBACX;gBACF;YACF,CAAC;IACH;IAEA,IAAI,aAAa,CAAC,mBAAmB,gBAAgB;QACnD,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAGxB,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOlC;IAEA,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAS,OAAO,UAAU,MAAM;QAAC;QAC1C;YAAE,OAAO;YAAa,OAAO;QAAO;QACpC;YAAE,OAAO;YAAa,OAAO;QAAM;KACpC;IAED,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;;wBACb,CAAC;4BACA,IAAI,CAAC,aAAa;gCAChB,QAAQ,GAAG,CAAC;4BACd;4BACA,OAAO;wBACT,CAAC;wBACA,CAAC,cACA,yDAAyD;sCACzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;iDAIxB;;8CACE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,aAAa;;;;;;8DAC/B,8OAAC,kIAAA,CAAA,iBAAc;8DACZ,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;sDAG1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAuB,OAAO,QAAQ;;;;;;wDACnD,8BACC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS;sEACV;;;;;;;;;;;;8DAKL,8OAAC;oDAAI,WAAU;8DACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4DAEC,WAAU;;8EAEV,8OAAC;oEAAK,WAAU;8EAAiB,KAAK,KAAK;;;;;;8EAC3C,8OAAC;oEAAK,WAAU;8EACb,KAAK,KAAK;;;;;;;2DALR,KAAK,KAAK;;;;;;;;;;8DAUrB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAiB,aAAa;;;;;;sEAC3C,8OAAC;4DAAE,WAAU;sEACV,aAAa;;;;;;;;;;;;8DAGlB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAC1B,8OAAC;oEACC,MAAM,CAAC,OAAO,EAAE,aAAa,OAAO;oEACpC,WAAU;8EAET,aAAa;;;;;;;;;;;;wDAGjB,aAAa,UAAU,SAAS,0BAC/B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAQ;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAC9B,8OAAC;oEACC,MAAM,YAAY,QAAQ,CAAC,OAAO,CAAC,KAAK;oEACxC,QAAO;oEACP,KAAI;oEACJ,WAAU;8EAET,YAAY,QAAQ,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;wDAIxC,aAAa,UAAU,OAAO,0BAC7B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAC3B,8OAAC;8EAAM,YAAY,QAAQ,CAAC,KAAK,CAAC,KAAK;;;;;;;;;;;;wDAG1C,aAAa,UAAU,WAAW,0BACjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,wNAAA,CAAA,gBAAa;oEACZ,MAAM;oEACN,WAAU;;;;;;8EAEZ,8OAAC;;wEACE,YAAY,QAAQ,CAAC,SAAS,CAAC,QAAQ;wEAAC;wEAAE;wEAC1C,YAAY,QAAQ,CAAC,SAAS,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQpD,8OAAC,gIAAA,CAAA,OAAI;oCAAC,cAAa;oCAAQ,WAAU;;sDACnC,8OAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;sDAClB,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,WAAU;;kEAEV,8OAAC,4MAAA,CAAA,UAAO;wDAAC,MAAM;;;;;;oDAAM;;;;;;;;;;;;sDAGzB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;;gDAChB,8BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEACJ,SAAQ;oEACR,WAAU;8EACX;;;;;;8EAGD,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,aAAY;oEACZ,WAAU;oEACV,OAAO;oEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sEAGlD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEACJ,SAAQ;oEACR,WAAU;8EACX;;;;;;8EAGD,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,WAAU;oEACV,OAAO;oEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sEAGjD,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS;gEACP,eAAe;gEACf,cAAc;4DAChB;sEACD;;;;;;;;;;;;8DAKL,8OAAC;oDAAI,WAAU;8DACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;4DAEC,WAAU;;8EAEV,8OAAC,6HAAA,CAAA,UAAK;oEACJ,KAAK,KAAK,QAAQ;oEAClB,KAAK,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE;oEACtC,IAAI;oEACJ,OAAM;oEACN,WAAU;oEACV,gBAAa;;;;;;gEAEd,8BACC,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS,IAAM,gBAAgB;kFAE/B,cAAA,8OAAC,2MAAA,CAAA,OAAI;4EAAC,MAAM;;;;;;;;;;;;;;;;;2DAlBb,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAgC3B,8BACC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,MAAM;oBAAkB,cAAc;8BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,cAAW;kDAAC;;;;;;kDACb,8OAAC,kIAAA,CAAA,oBAAiB;kDAAC;;;;;;;;;;;;0CAIrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,kBAAkB,aAAa,QAAQ;oDAC5C,KAAI;oDACJ,IAAI;oDACJ,WAAU;;;;;;;;;;;0DAGd,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,QAAO;gDACP,UAAU,CAAC;oDACT,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;wDACvC,MAAM,SAAS,IAAI;wDACnB,OAAO,MAAM,GAAG,CAAC;4DACf,kBAAkB,MAAM,MAAM,EAAE;wDAClC;wDACA,OAAO,aAAa,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;oDACxC;gDACF;;;;;;;;;;;;kDAGJ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAU;;;;;;0DACzB,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAGpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAE;oDAAU,aAAa,MAAM,CAAC,QAAQ;oDAAC;;;;;;;0DAC1C,8OAAC;;oDAAE;oDACK;oDACL,IAAI,KAAK,aAAa,SAAS,EAAG,kBAAkB;oDAAI;oDAAI;;;;;;;;;;;;;;;;;;;0CAKnE,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,cAAW;wCAAC,OAAO;kDAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,SAAQ;sDAAY;;;;;;;;;;;kDAI5C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;8BAS1D,8OAAC,kIAAA,CAAA,SAAM;oBACL,MAAM;oBACN,cAAc;8BAEd,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,cAAW;kDAAC;;;;;;kDACb,8OAAC,kIAAA,CAAA,oBAAiB;kDAAC;;;;;;;;;;;;0CAIrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC,kIAAA,CAAA,cAAW;gEAAC,KAAK,cAAc,SAAS;;;;;;0EACzC,8OAAC,kIAAA,CAAA,iBAAc;0EACZ,cAAc,QAAQ,EAAE,OAAO,GAAG;;;;;;;;;;;;kEAGvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,IAAG;wDACH,QAAO;wDACP,UAAU,OAAO;4DACf,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;gEACvC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;gEAE9B,2BAA2B;gEAC3B,MAAM,SAAS,IAAI;gEACnB,OAAO,MAAM,GAAG,CAAC;oEACf,yBACE,aACA,MAAM,MAAM,EAAE;gEAElB;gEACA,OAAO,aAAa,CAAC;gEAErB,6BAA6B;gEAC7B,IAAI;oEACF,QAAQ,GAAG,CAAC;oEACZ,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,MAAM,aAAa;oEAC1C,IAAI,OAAO;wEACT,QAAQ,KAAK,CAAC,6BAA6B;oEAC3C,qCAAqC;oEACvC,OAAO,IAAI,KAAK;wEACd,QAAQ,GAAG,CACT,mCACA;wEAEF,yBAAyB,aAAa;wEACtC,uFAAuF;wEACvF,IAAI,cAAc;4EAChB,QAAQ,GAAG,CACT;4EAEF,eAAe,CAAC,OACd,OAAO;oFAAE,GAAG,IAAI;oFAAE,WAAW;gFAAI,IAAI;wEAEzC;oEACF;gEACF,EAAE,OAAO,OAAO;oEACd,QAAQ,KAAK,CACX,iCACA;gEAEJ;4DACF;wDACF;;;;;;;;;;;;;;;;;;kDAIN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAM;;;;;;0DACrB,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,cAAa;gDACb,OAAO,cAAc,GAAG,IAAI;gDAC5B,UAAU,CAAC,IACT,yBAAyB,OAAO,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAKpD,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDAIpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,cAAc,KAAK,IAAI;gDAC9B,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAK/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,IAAG;gEACH,SAAS,cAAc,QAAQ,EAAE,SAAS;gEAC1C,iBAAiB,CAAC,UAChB,oBAAoB,WAAW,YAAY;;;;;;0EAG/C,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAiB,WAAU;0EACvC,cAAc,QAAQ,EAAE,SAAS,WAC9B,WACA;;;;;;;;;;;;;;;;;;0DAIV,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,cAAa;gDACb,OAAO,cAAc,QAAQ,EAAE,SAAS,SAAS;gDACjD,UAAU,CAAC,IACT,oBAAoB,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;gDAExD,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,IAAG;gEACH,SAAS,cAAc,QAAQ,EAAE,OAAO;gEACxC,iBAAiB,CAAC,UAChB,oBAAoB,SAAS,YAAY;;;;;;0EAG7C,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAe,WAAU;0EACrC,cAAc,QAAQ,EAAE,OAAO,WAC5B,WACA;;;;;;;;;;;;;;;;;;0DAIV,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,cAAa;gDACb,OAAO,cAAc,QAAQ,EAAE,OAAO,SAAS;gDAC/C,UAAU,CAAC,IACT,oBAAoB,SAAS,SAAS,EAAE,MAAM,CAAC,KAAK;gDAEtD,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;kEAC3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,IAAG;gEACH,SAAS,cAAc,QAAQ,EAAE,WAAW;gEAC5C,iBAAiB,CAAC,UAChB,oBAAoB,aAAa,YAAY;;;;;;0EAGjD,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAmB,WAAU;0EACzC,cAAc,QAAQ,EAAE,WAAW,WAChC,WACA;;;;;;;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,cAAa;wDACb,OAAO,cAAc,QAAQ,EAAE,WAAW,YAAY;wDACtD,UAAU,CAAC,IACT,oBACE,aACA,YACA,EAAE,MAAM,CAAC,KAAK;wDAGlB,aAAY;;;;;;kEAEd,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,cAAa;wDACb,OAAO,cAAc,QAAQ,EAAE,WAAW,YAAY;wDACtD,UAAU,CAAC,IACT,oBACE,aACA,YACA,EAAE,MAAM,CAAC,KAAK;wDAGlB,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAKpB,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,cAAW;wCAAC,OAAO;kDAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,SAAQ;sDAAY;;;;;;;;;;;kDAI5C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAS;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvE", "debugId": null}}]}